import 'dart:io';
import 'dart:math';
import 'dart:ui';
import 'package:chat_app/blocs/auth_bloc/auth_bloc.dart';
import 'package:chat_app/models/auths/user_model.dart';
import 'package:chat_app/models/chats/conversatoin_model.dart';
import 'package:chat_app/models/payments/transaction_model.dart';
import 'package:chat_app/repositories/home_repositories.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/app_styles.dart';
import 'package:chat_app/utils/extensions.dart';
import 'package:chat_app/utils/navigator_service.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';

import 'package:firebase_storage/firebase_storage.dart';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:mime_type/mime_type.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';
import 'platform_helper.dart';
import 'package:toastification/toastification.dart';
import 'package:upi_pay/api.dart';
import 'package:upi_pay/types/discovery.dart';
import 'package:upi_pay/types/meta.dart';
import 'package:upi_pay/types/response.dart';
// import 'package:video_thumbnail/video_thumbnail.dart';

import '../widgets/custom_dialog.dart';
import 'firestore_service.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

enum SCREEN { getStarted }

enum ImagePickerType { camera, gallery }

class Helper {
  bool isEmail(String em) {
    String p =
        r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$';
    RegExp regExp = RegExp(p);
    return regExp.hasMatch(em.trim());
  }

  bool weakPassword(String em) {
    return em.trim().length < 8;
  }

  static String formatDateTime(String dateTime,
      {String? fomat, bool isHumanReadable = false}) {
    if (dateTime.isEmpty) {
      return "";
    }
    var date = DateTime.parse(dateTime).toLocal();
    var now = DateTime.now();

    // If a specific format is provided, use the original logic
    if (fomat != null) {
      String pattern = fomat;
      if (date.isToday()) {
        pattern = "hh:mm a";
      }
      if (now.day - date.day == 1) {
        return "${"key_yesterday".tr()}, ${DateFormat("hh:mm a").format(date)}";
      }
      return DateFormat(pattern).format(date);
    }

    // New logic for conversation list timestamps (when no format is specified)
    // Check if it's today
    if (date.isToday()) {
      // For today messages: display timings with minutes and AM/PM (e.g., "6:30AM")
      var hour = date.hour;
      var minute = date.minute;
      var period = hour >= 12 ? 'PM' : 'AM';
      var displayHour = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);
      var minuteStr = minute.toString().padLeft(2, '0');
      return "$displayHour:$minuteStr $period";
    }

    // Check if it's yesterday
    var yesterday = now.subtract(Duration(days: 1));
    if (date.year == yesterday.year &&
        date.month == yesterday.month &&
        date.day == yesterday.day) {
      // For yesterday messages: display only "Yesterday"
      return "key_yesterday".tr();
    }

    // For other old messages: display only date (e.g., "7/8/25")
    return DateFormat("M/d/yy").format(date);
  }

  static DateTime formatStringToDateTime(BuildContext context, String dateTime,
      {String? format}) {
    try {
      var locale = context.locale.toStringWithSeparator();
      return DateFormat(format ?? "yyyy/MM/dd", locale)
          .parse(dateTime)
          .toLocal();
    } catch (e) {
      return DateTime(0);
    }
  }

  static String formatDateTimeToString(BuildContext context, DateTime dateTime,
      {String? newPattern, DateFormat? dateFormat}) {
    if (dateFormat != null) {
      return dateFormat.format(dateTime);
    }
    var locale = context.locale;
    return DateFormat(
      newPattern,
      locale.languageCode,
    ).format(dateTime);
  }

  static String formatUtcTime(
      {required String dateUtc,
      required format,
      required BuildContext context,
      bool isHumanTime = false,
      String? newPattern}) {
    try {
      var locale = context.locale.toStringWithSeparator();
      var dateTime = DateFormat(
        newPattern ?? "yyyy-MM-dd'T'HH:mm:ss",
      ).parseUtc(dateUtc);
      var dateLocal = dateTime.toLocal();
      String? _format;

      if (format == 'dd/MM/yy') {
        return DateFormat.yMMMMd(locale).format(dateLocal);
      } else if (format == 'dd-MM-yyyy hh:mm a') {
        return DateFormat.yMMMMd(locale).add_jm().format(dateLocal);
      }

      return DateFormat(_format ?? format, locale).format(dateLocal);
    } catch (e) {
      return '-:--';
    }
  }

  static String convertTimeToHourOrDay(
      {required String dateTime, required String format}) {
    try {
      var date = DateFormat(format).parse(dateTime);

      final x = DateFormat(format).format(DateTime.now());
      final dateNow = DateFormat(format).parse(x);
      final day = dateNow.difference(date).inDays;
      if (day == 0) {
        final hours = dateNow.difference(date).inHours;
        if (hours == 0) {
          final minutes = dateNow.difference(date).inMinutes;
          if (minutes == 0) {
            return "${dateNow.difference(date).inSeconds}sec";
          } else {
            return "${minutes}min";
          }
        } else {
          return "${hours.abs()}h";
        }
      }
      return "${dateNow.difference(date).inDays}d";
    } catch (e) {
      return "-:--";
    }
  }

  static String formatBigNumber2(int? number) {
    try {
      var formatter = NumberFormat.decimalPattern('en_us');
      return formatter.format(number);
    } catch (e) {
      return number == null ? "0" : "$number";
    }
  }

  static setStatusBarColor(Color color) {
    SystemChrome.setSystemUIOverlayStyle(
        SystemUiOverlayStyle(statusBarColor: color // status bar color
            ));
  }

  static WidgetsBinding? _widgetsBinding;
  static void preserve({required WidgetsBinding widgetsBinding}) {
    _widgetsBinding = widgetsBinding;
    _widgetsBinding?.deferFirstFrame();
  }

  static Future<void> remove() async {
    if (_widgetsBinding != null) {
      await Future.delayed(const Duration(milliseconds: 500));
    }
    _widgetsBinding?.allowFirstFrame();
    _widgetsBinding = null;
  }

  static Future<String> saveFile(List<int> bytes) async {
    if (kIsWeb) {
      // On web, we can't save to file system directly
      // Return a data URL or handle differently
      return "web_file_${DateTime.now().millisecondsSinceEpoch}.png";
    } else {
      var documentPath = await PlatformHelper.getDownloadsDirectoryCompat();
      if (documentPath != null) {
        var filePath =
            "${documentPath.path}/thumbnail_${DateTime.now().millisecondsSinceEpoch}.png";
        var file = File(filePath);
        await file.writeAsBytes(bytes);
        return filePath;
      }
      return "error_saving_file";
    }
  }

  static List<String> extractMetionsFromString(String text) {
    try {
      RegExp mentionExp = RegExp(r"@[^\s]+");
      List<String?> mentions = mentionExp.allMatches(text).map((match) {
        if (match.group(0) != null) {
          return match.group(0)!.substring(1);
        }
        return null;
      }).toList();
      return mentions.whereType<String>().toList();
    } catch (e) {
      return [];
    }
  }

  static List<String> extractHashtagsFromString(String text) {
    List<String> hashtags = [];
    RegExp hashtagExp = RegExp(r"#[^\s]+");
    try {
      hashtags = hashtagExp
          .allMatches(text)
          .map((match) {
            if (match.group(0) != null) {
              return match.group(0)!.substring(1);
            }
            return '';
          })
          .where((element) => element.isNotEmpty)
          .toList();
      return hashtags;
    } catch (e) {
      return [];
    }
  }

  static bool checkStringIsNumberFormat(String input) {
    final RegExp numberPattern = RegExp(r'^[0-9]+$');

    if (numberPattern.hasMatch(input)) {
      return true;
    } else {
      return false;
    }
  }

  // static Future<File> getVideoThumbnail(File file) async {
  //   final uint8list = await VideoThumbnail.thumbnailData(
  //     video: file.path,
  //     imageFormat: ImageFormat.JPEG,
  //     quality: 60,
  //   );
  //   return saveBytesToTemporaryFile(
  //       uint8list: uint8list!, extension: file.path.split('.').last);
  // }

  static Future<File?> saveBytesToTemporaryFile(
      {required Uint8List uint8list,
      required String extension,
      Directory? directory}) async {
    if (kIsWeb) {
      // On web, we can't save files to local file system
      return null;
    }

    var documents =
        directory ?? await PlatformHelper.getTemporaryDirectoryCompat();
    if (documents == null) {
      return null;
    }

    var output =
        '${documents.path}/file_${DateTime.now().millisecondsSinceEpoch}.$extension';
    var file = File(output);
    await file.writeAsBytes(uint8list);
    return file;
  }

  static String formatFileSize(int fileSizeInBytes) {
    if (fileSizeInBytes < 1024) {
      return '$fileSizeInBytes B';
    } else if (fileSizeInBytes < 1024 * 1024) {
      double fileSizeInKB = fileSizeInBytes / 1024;
      return '${fileSizeInKB.toStringAsFixed(2)} KB';
    } else if (fileSizeInBytes < 1024 * 1024 * 1024) {
      double fileSizeInMB = fileSizeInBytes / (1024 * 1024);
      return '${fileSizeInMB.toStringAsFixed(2)} MB';
    } else {
      double fileSizeInGB = fileSizeInBytes / (1024 * 1024 * 1024);
      return '${fileSizeInGB.toStringAsFixed(2)} GB';
    }
  }

  static String? chechFileExist(ChatModel chat, String directoryPath) {
    try {
      String url = chat.message!;

      // Use Uri.parse() to parse the URL
      Uri uri = Uri.parse(url);

      // Extract the 'path' component from the URI
      String path = uri.path;
      path = path.split('/').last;

      path = Uri.decodeComponent(path);

      File file = File('$directoryPath/$path');
      if (file.existsSync()) {
        return file.path;
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  static void showDialogSuccessMessages({
    required BuildContext context,
    String? title,
    String message = '',
    Function? onClose,
    bool isShowSecondButton = false,
    Function? onPressPrimaryButton,
    onPressSecondButton,
    String? labelPrimary,
    labelSecondary,
  }) {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => CustomDialog(
              title: title ?? "",
              descriptions: message,
              showTitle: false,
              onClose: onClose,
              isShowSecondButton: isShowSecondButton,
              onPressPrimaryButton: onPressPrimaryButton,
              onPressSecondButton: onPressSecondButton,
              labelPrimary: labelPrimary,
              labelSecondary: labelSecondary,
            ));
  }

  static showActionDialog({
    required BuildContext context,
    required String title,
    required String message,
    required Function onClose,
    required Function onConfirm,
    bool barrierDismissible = true,
    Widget? image,
    String? labelPrimary,
    String? labelSecondary,
    bool showTitle = true,
  }) {
    return showDialog(
        context: context,
        barrierDismissible: barrierDismissible,
        builder: (context) => CustomDialog(
              title: title,
              image: image,
              descriptions: message,
              onClose: onClose,
              barrierDismissible: barrierDismissible,
              isShowSecondButton: true,
              onPressPrimaryButton: onConfirm,
              onPressSecondButton: onClose,
              labelPrimary: labelPrimary,
              labelSecondary: labelSecondary,
              showTitle: showTitle,
            ));
  }

  static Future<File?> downloadMedia(
      {required String link,
      required Function(String) onCompleted,
      required Function onError,
      bool isTempory = false,
      bool useCache = true}) async {
    try {
      if (kIsWeb) {
        // On web, we can't save files to local file system
        // Instead, we can trigger a download or handle differently
        onCompleted(link); // Return the URL itself
        return null;
      }

      final directory = isTempory
          ? (await PlatformHelper.getTemporaryDirectoryCompat())
          : await PlatformHelper.getApplicationDocumentsDirectoryCompat();

      if (directory == null) {
        onError();
        return null;
      }

      // if (await file.exists()) {
      //   file.delete();
      // }
      String url = link;

      // Use Uri.parse() to parse the URL
      Uri uri = Uri.parse(url);

      // Extract the 'path' component from the URI
      String path = uri.path;

      path = path.split('/').last;

      path = Uri.decodeComponent(path);

      File file = File('${directory.path}/$path');
      if (useCache) {
        if (file.existsSync()) {
          return file;
        }
      }
      file.create(recursive: true);
      Reference storageReference = FirestoreService.storageRef.child(path);
      await storageReference.writeToFile(file);
      onCompleted(file.path);
      return file;
    } catch (e) {
      showToast(e.toString());
      onError();
      return null;
    }
  }

  static int getRandomNumber() {
    Random random = Random();
    var value = random.nextInt(1000000000) * 10;
    return value;
    // var x = DateTime.now().millisecondsSinceEpoch;
    // var value = int.parse(x.toString().substring(0, 10));
    // return value;
  }

  static String getSchoolCodeFromSid(String sid) {
    RegExp regex = RegExp(r'[A-Z]+'); // Match one or more uppercase letters

    Match? match = regex.firstMatch(sid);

    if (match != null) {
      String extractedString = match.group(0)!;
      return extractedString;
    } else {
      return "NULL";
    }
  }

  static String getStudentIdFromSid(String sid) {
    RegExp regex = RegExp(r'\d+'); // Match one or more digits

    Match? match = regex.firstMatch(sid);

    if (match != null) {
      String extractedString = match.group(0)!;
      return extractedString;
    } else {
      return "NULL";
    }
  }

  static String? getTypeOfMessage(String message) {
    if (isUrl(message)) {
      var uri = Uri.parse(message);
      var type = mime(uri.path);
      if (type != null) {
        return type.split('/').first;
      }
      return null;
    }
    return null;
  }

  static Future generatePdfFromOrder(
      {required String orderId,
      required String schoolCode,
      required String schoolName,
      TransactionModel? transactionModel,
      bool openFile = true}) async {
    late TransactionModel model;
    if (transactionModel != null) {
      model = transactionModel;
    } else {
      var response = await HomeRepositories().checkDeepLinkPaymentStatus(body: {
        "school_code": schoolCode,
        "order_id": orderId,
      });
      var body = response['data'][0];

      model = TransactionModel.fromJson(body);
    }

    final pdf = pw.Document();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            mainAxisSize: pw.MainAxisSize.min,
            children: [
              pw.Center(
                child: pw.Text(
                  schoolName,
                  style: pw.TextStyle(
                    fontSize: 20,
                    color: PdfColor.fromInt(Colors.black.value),
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              ),
              pw.SizedBox(height: 16),
              pw.Center(
                child: pw.Text(
                  "key_receipt".tr(),
                  style: pw.TextStyle(
                    fontSize: 16,
                    color: PdfColor.fromInt(Colors.black.value),
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              ),
              pw.SizedBox(height: 16),

              buildItemTransactionPw(
                  label: "key_transaction_status".tr(),
                  value: "${model.status}"),
              buildItemTransactionPw(
                  label: "key_fee_amount".tr(), value: "${model.feeAmount}"),
              buildItemTransactionPw(
                  label: "key_convinience_fee".tr(),
                  value: "${model.conveyanceFee}"),
              buildItemTransactionPw(
                  label: "key_total_paid".tr(), value: "${model.paidAmount}"),
              buildItemTransactionPw(
                  label: "key_transaction_date".tr(),
                  value: "${model.transactionAt}"),
              buildItemTransactionPw(
                  label: "key_order_id".tr(), value: "${model.orderId}"),
              buildItemTransactionPw(
                  label: "key_transaction_id".tr(),
                  value: "${model.transactionId}"),
              buildItemTransactionPw(
                  label: "key_student_id".tr(), value: "${model.studentId}"),
              // buildItemTransactionPw(
              //     label: "key_student_name".tr(), value: "${model.studen}"),
            ],
          );
        },
      ),
    );
    var result = await pdf.save(); // Page
    var file = await saveBytesToTemporaryFile(
      uint8list: result,
      extension: "pdf",
      // directory: await getDownloadsDirectory(),
    );
    if (openFile && file != null) {
      await PlatformHelper.openFile(file.path);
    }
    return file;
  }

  static buildItemTransactionPw(
      {required String label, required String value}) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          label,
          style: pw.TextStyle(
              fontSize: 14, color: PdfColor.fromInt(Colors.grey.value)),
        ),
        pw.Padding(
          padding: pw.EdgeInsets.only(top: 4, bottom: 10),
          child: pw.Text(
            value,
            style: pw.TextStyle(
              fontSize: 14,
              color: PdfColor.fromInt(Colors.black.value),
            ),
            // style: AppStyles.textSize14(),
          ),
        ),
      ],
    );
  }

  static Future<String> compressImage(String path) async {
    if (kIsWeb) {
      // On web, compression might not be supported or needed
      return path;
    }

    var result = await FlutterImageCompress.compressWithFile(
      path,
      minWidth: 1200,
      minHeight: 1200,
      quality: 70,
    );
    if (result != null) {
      var file = await saveBytesToTemporaryFile(
        uint8list: result,
        extension: path.split('.').last,
      );
      if (file != null) {
        print("compress image size: ${formatFileSize(file.lengthSync())}");
        return file.path;
      }
    }
    return path;
  }

  Widget appWidget(ApplicationMeta appMeta) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        appMeta.iconImage(48), // Logo
        Container(
          margin: EdgeInsets.only(top: 4),
          alignment: Alignment.center,
          child: Text(
            appMeta.upiApplication.getAppName(),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  static showUPIApps(BuildContext context) async {
    final upiPay = UpiPay();
    List<ApplicationMeta>? _apps = await upiPay.getInstalledUpiApplications(
        statusType: UpiApplicationDiscoveryAppStatusType.all);
    final result = await showDialog(
        context: context,
        builder: (_) {
          return Dialog(
            child: Container(
              margin: EdgeInsets.only(top: 32, bottom: 32),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Container(
                    margin: EdgeInsets.only(bottom: 12),
                    child: Text(
                      'Pay Using',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                  _appsGrid(_apps.map((e) => e).toList(), (app) {
                    Navigator.pop(context, app);
                  }),
                ],
              ),
            ),
          );
        });
    if (result != null) {
      return result;
    }
    return null;
  }

  static GridView _appsGrid(
      List<ApplicationMeta> apps, Function(ApplicationMeta) onTap) {
    apps.sort((a, b) => a.upiApplication
        .getAppName()
        .toLowerCase()
        .compareTo(b.upiApplication.getAppName().toLowerCase()));
    return GridView.count(
      crossAxisCount: 4,
      shrinkWrap: true,
      mainAxisSpacing: 4,
      crossAxisSpacing: 4,
      // childAspectRatio: 1.6,
      physics: NeverScrollableScrollPhysics(),
      children: apps
          .map(
            (it) => Material(
              key: ObjectKey(it.upiApplication),
              // color: Colors.grey[200],
              child: InkWell(
                onTap: Platform.isAndroid ? () async => await onTap(it) : null,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    it.iconImage(48),
                    Container(
                      margin: EdgeInsets.only(top: 4),
                      alignment: Alignment.center,
                      child: Text(
                        it.upiApplication.getAppName(),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          )
          .toList(),
    );
  }

  static Future processUpiPayment(
      {required Function(UpiTransactionResponse?) onSuccess,
      required Function(String) onError,
      required String deeplink}) async {
    try {
      Uri uri = Uri.parse(deeplink);
      var schoolName = uri.queryParameters['pn'];
      var amount = uri.queryParameters['am'];
      var mc = uri.queryParameters['mc'];
      var tc = uri.queryParameters['tr'];
      var orderId = uri.queryParameters['tn'];
      final upiPay = UpiPay();
      final app = await showUPIApps(getContext());
      if (app != null) {
        final UpiTransactionResponse response =
            await upiPay.initiateTransaction(
          amount: amount ?? "",
          receiverName: schoolName ?? "",
          receiverUpiAddress: 'EASYBILLPAYSURENA@ICICI',
          transactionRef: tc ?? "",
          transactionNote: orderId,
          app: app,
        );
        onSuccess(response);
      }
      // final res = await EasyUpiPaymentPlatform.instance.startPayment(
      //   EasyUpiPaymentModel(
      //     // transactionId: orderId,
      //     transactionRefId: tc,
      //     payeeMerchantCode: mc,
      //     payeeVpa: 'EASYBILLPAYSURENA@ICICI',
      //     payeeName: schoolName ?? "",
      //     amount: double.tryParse(amount ?? "") ?? 0.0,
      //     description: orderId,
      //   ),
      // );
    } catch (e) {
      onError(e.toString());
    }
  }
}

enum ToastType { success, errror, info }

hideKeyboard(BuildContext context) {
  FocusScope.of(context).requestFocus(FocusNode());
}

getFileName(String path) {
  return path.split('/').last;
}

double getWidth(BuildContext context) {
  return MediaQuery.of(context).size.width;
}

double getHeight(BuildContext context) {
  return MediaQuery.of(context).size.height;
}

String getFullName(String? firstName, String? lastName, {bool cut = true}) {
  var fullName = "${firstName ?? ""}${lastName != null ? " $lastName" : ""}";
  if (fullName.length > 20 && cut) {
    return fullName.substring(0, 20) + "...";
  }
  return fullName;
}

moveCursorToEnd(TextEditingController controller) {
  controller.selection =
      TextSelection.fromPosition(TextPosition(offset: controller.text.length));
}

bool isUrl(String string) {
  final urlPattern = RegExp(
      r'^(https?|ftp)://'
      r'(?:(?:[A-Z0-9][A-Z0-9_-]*)(?::(?:[A-Z0-9][A-Z0-9_-]*))?@)?'
      r'(?:'
      r'(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+(?:[A-Z]{2,6}\.?|[A-Z0-9-]{2,}\.?)|'
      r'localhost|'
      r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}'
      r')(?::\d+)?'
      r'(?:/?|[/?]\S+)$',
      caseSensitive: false);

  return urlPattern.hasMatch(string);
}

String formatDistance(int distance) {
  if (distance >= 1000) {
    double km = distance / 1000;
    return '${km.toStringAsFixed(2)} km';
  } else {
    return '$distance m';
  }
}

exitLandScape() async {
  await SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
      overlays: SystemUiOverlay.values);
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
}

enterLandScape() async {
  await SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: []);
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.landscapeRight,
    DeviceOrientation.landscapeLeft,
  ]);
}

restoreSystemUIOverlays() async {
  await SystemChrome.restoreSystemUIOverlays();
}

isLandScape(BuildContext context) {
  return MediaQuery.of(context).orientation == Orientation.landscape;
}

bool isFile(String path) {
  return File(path).existsSync();
}

isKeyboardAppears(BuildContext context) {
  var bottom = MediaQuery.of(context).viewInsets.bottom;
  return bottom > 0;
}

double getKeyboardHeight(BuildContext context) {
  var bottom = MediaQuery.of(context).viewInsets.bottom;
  return bottom;
}

String convertHtmlToText(String input) {
  // Replace <span> tags with appropriate formatting
  String result = input.replaceAllMapped(
    RegExp(r'<span.*?class="(hash-tag|mention)".*?>(.*?)<\/span>'),
    (match) {
      String? tagType = match.group(1);
      String? content = match.group(2);

      if (tagType == 'hash-tag') {
        return '$content';
      } else if (tagType == 'mention') {
        return '$content';
      } else {
        return content ?? "";
      }
    },
  );

  // Replace &nbsp; with space
  result = result.replaceAll('&nbsp;', ' ');

  return result;
}

bool containsHtmlTags(String input) {
  RegExp htmlTagPattern = RegExp(r'<[^>]*>');
  return htmlTagPattern.hasMatch(input);
}

showToast(String msg) {
  toastification.show(
    alignment: Alignment.topCenter,
    context: getContext(),
    type: ToastificationType.info,
    style: ToastificationStyle.simple,
    title: Text(msg),
    margin: EdgeInsets.all(0),
    autoCloseDuration: const Duration(seconds: 2),
  );
}

bool isImageLink(String link) {
  // List of common image file extensions
  List<String> imageExtensions = [
    '.jpg',
    '.jpeg',
    '.png',
    '.gif',
    '.bmp',
    '.webp'
  ];

  // Parse the link using Uri
  Uri uri = Uri.parse(link);

  // Get the path from the URI
  String path = uri.path.toLowerCase();

  // Check if the link ends with any of the image extensions
  return imageExtensions.any((extension) => path.endsWith(extension));
}

String getLinkType(String link) {
  // Parse the link using Uri
  Uri uri = Uri.parse(link);

  // Get the path from the URI
  String path = uri.path.toLowerCase();

  // Check if the link ends with any of the image extensions
  return path.split('.').last;
}

UserModel getProfile(BuildContext context) {
  return BlocProvider.of<AuthBloc>(context).state.userModel!;
}
