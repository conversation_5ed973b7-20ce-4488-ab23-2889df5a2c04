import 'package:cached_network_image/cached_network_image.dart';
import 'package:chat_app/blocs/auth_bloc/auth_bloc.dart';
import 'package:chat_app/export.dart';
import 'package:chat_app/models/auths/user_model.dart';
import 'package:chat_app/models/chats/conversatoin_model.dart';
import 'package:chat_app/screens/chat/absentees_messages/absentees_mesages.dart';
import 'package:chat_app/screens/chat/activity/activity.dart';
import 'package:chat_app/screens/chat/chat_details/chat_details.dart';
import 'package:chat_app/screens/chat/chat_details/widgets/change_room_dialog.dart';
import 'package:chat_app/screens/chat/create_user/create_user.dart';
import 'package:chat_app/screens/chat/search/search_parent.dart';
import 'package:chat_app/screens/chat/send_birthday/send_birthday.dart';
import 'package:chat_app/screens/chat/send_bulk_messages/send_bulk_messages.dart';
import 'package:chat_app/screens/chat/update_user/update_user.dart';
import 'package:chat_app/screens/settings/settings.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/firestore_service.dart';
import 'package:chat_app/widgets/instagram_viewer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'dart:async';
// ignore: depend_on_referenced_packages
import 'package:async/async.dart';

class ChatPage extends StatefulWidget {
  const ChatPage({super.key});

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> {
  int _badge = 0;
  // Create a StreamGroup and add your streams to it
  final StreamGroup<List<ActivityModel>> _streamGroup =
      StreamGroup<List<ActivityModel>>();

  int _currentTab = 0;

  @override
  void initState() {
    _init();
    super.initState();
  }

  _init() async {
    var user = BlocProvider.of<AuthBloc>(context).state.userModel;
    if (user?.type == AppConstants.parentType) {
      var students = BlocProvider.of<AuthBloc>(context).state.students;

      for (var element in students!) {
        // var count =
        //     await FirestoreService.getUnReadActivityCount(userId: element.sid!);
        // total += count;

        var stream =
            FirestoreService.getActivityStreamStudent(receiverId: element.sid!);
        _streamGroup.add(stream);
      }
      _streamGroup.stream.listen((event) {
        var total =
            event.where((element) => element.readAt == null).toList().length;
        setState(() {
          _badge = total;
        });
      });
    }
  }

  @override
  void dispose() {
    _streamGroup.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(builder: (context, state) {
      return Scaffold(
        backgroundColor: AppColors.backgroundScaffold,
        body: SafeArea(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
            child: Column(
              children: [
                Expanded(child:
                    BlocBuilder<AuthBloc, AuthState>(builder: (context, state) {
                  if (state.userModel != null) {
                    bool isTeacher =
                        state.userModel?.type == AppConstants.teacherType;
                    if (isTeacher == false) {
                      if (state.students?.length == 1) {
                        return ListRoom(
                            userId: state.students!.first.docId!,
                            isTeacher: isTeacher);
                      }
                      return DefaultTabController(
                        length: state.students!.length,
                        child: Column(
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(bottom: 12),
                              child: TabBar(
                                  onTap: (value) {
                                    _currentTab = value;
                                  },
                                  tabs: state.students!
                                      .map((e) => Tab(
                                            text: e.name,
                                          ))
                                      .toList()),
                            ),
                            Expanded(
                              child: TabBarView(
                                  children: state.students!
                                      .map((e) => ListRoom(
                                            isTeacher: isTeacher,
                                            userId: e.docId!,
                                          ))
                                      .toList()),
                            ),
                          ],
                        ),
                      );
                    }
                    return ListRoom(
                        userId: state.userModel!.docId!, isTeacher: isTeacher);
                  }
                  return Container();
                }))
              ],
            ),
          ),
        ),
      );
    });
  }
}

class ListRoom extends StatelessWidget {
  final String userId;
  final bool isTeacher;
  const ListRoom({super.key, required this.userId, required this.isTeacher});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<ConversationModel>>(
        stream: FirestoreService.getConversationStream(
            userId: userId, isTeacher: isTeacher),
        builder: (context, snapshot) {
          if (snapshot.hasError) {
            return _buildErrorState(snapshot.error.toString());
          }

          if (snapshot.connectionState == ConnectionState.waiting) {
            return _buildLoadingState();
          }

          if (snapshot.hasData) {
            var list = snapshot.data!;
            if (list.isEmpty) {
              return _buildEmptyState();
            }
            return _buildConversationList(list);
          }

          return _buildEmptyState();
        });
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              strokeWidth: 3,
            ),
          ),
          SizedBox(height: 16),
          Text(
            "Loading conversations...",
            style: AppStyles.textSize14(
              color: AppColors.grey,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Container(
        margin: EdgeInsets.all(24),
        padding: EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.red.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48,
            ),
            SizedBox(height: 16),
            Text(
              "Something went wrong",
              style: AppStyles.textSize16(
                color: Colors.red,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8),
            Text(
              error,
              style: AppStyles.textSize12(
                color: Colors.red.withOpacity(0.8),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Container(
        margin: EdgeInsets.all(24),
        padding: EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: AppColors.white.withOpacity(0.05),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: AppColors.white.withOpacity(0.1),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(50),
              ),
              child: Icon(
                Icons.chat_bubble_outline,
                color: AppColors.primary,
                size: 48,
              ),
            ),
            SizedBox(height: 20),
            Text(
              "key_empty".tr(),
              style: AppStyles.textSize18(
                color: AppColors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 8),
            Text(
              isTeacher
                  ? "Start a conversation with your students"
                  : "No conversations yet",
              style: AppStyles.textSize14(
                color: AppColors.white.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConversationList(List<ConversationModel> list) {
    return ListView.separated(
      padding: EdgeInsets.symmetric(vertical: 12, horizontal: 12),
      itemBuilder: (_, index) {
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 8,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: ConversationItem(
              conversationModel: list[index],
            ),
          ),
        );
      },
      separatorBuilder: (_, index) {
        return SizedBox(height: 12);
      },
      itemCount: list.length,
    );
  }
}

class ConversationItem extends StatelessWidget {
  final ConversationModel conversationModel;
  const ConversationItem({super.key, required this.conversationModel});

  /// Get initials from conversation title
  String _getInitials(String? title) {
    if (title == null || title.isEmpty) return "?";

    List<String> words =
        title.trim().split(' ').where((word) => word.isNotEmpty).toList();

    if (words.length >= 2) {
      String first = words[0].isNotEmpty ? words[0][0] : "";
      String second = words[1].isNotEmpty ? words[1][0] : "";
      if (first.isNotEmpty && second.isNotEmpty) {
        return "$first$second".toUpperCase();
      }
    }

    if (words.isNotEmpty && words[0].isNotEmpty) {
      return words[0][0].toUpperCase();
    }

    return "?";
  }

  /// Get avatar background color based on title
  Color _getAvatarColor(String? title) {
    if (title == null || title.isEmpty) return AppColors.grey;

    final colors = [
      AppColors.primary,
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.indigo,
      Colors.pink,
    ];

    try {
      int hash = title.hashCode;
      int index = hash.abs() % colors.length;
      return colors[index];
    } catch (e) {
      return AppColors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    bool isGroup = conversationModel.users!.length > 2;
    String title = conversationModel.title ?? "";
    var user = BlocProvider.of<AuthBloc>(context).state.userModel;
    if (isGroup == false) {
      try {
        title = conversationModel.users!
                .firstWhere((element) => element.docId != user?.docId)
                .name ??
            "";
        if (user?.type == AppConstants.parentType) {
          title = conversationModel.users!
                  .firstWhere((element) =>
                      user!.students!.contains(element.docId) == false)
                  .name ??
              "";
        }
      } catch (e) {}
    }
    return InkWell(
      onTap: () {
        push(
            context,
            ChatDetails(
              converationId: conversationModel.id,
              conversationModel: conversationModel,
            ));
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 14, vertical: 10),
        decoration: BoxDecoration(
          color: AppColors.white.withOpacity(AppColors.isDark ? 0.3 : 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Padding(
              padding: const EdgeInsets.only(right: 10),
              child: Builder(builder: (context) {
                if (conversationModel.image?.isNotEmpty ?? false) {
                  return Container(
                    margin: EdgeInsets.only(right: 10),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(1000),
                      child: InstaImageViewerCustom(
                        link: "${conversationModel.image}",
                        child: CachedNetworkImage(
                          imageUrl: "${conversationModel.image}",
                          width: 50,
                          height: 50,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  );
                }
                return Container(
                  width: 50,
                  height: 50,
                  margin: EdgeInsets.only(right: 12),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _getAvatarColor(conversationModel.title),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 4,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: conversationModel.image != null &&
                          conversationModel.image!.isNotEmpty
                      ? ClipOval(
                          child: Image.network(
                            conversationModel.image!,
                            width: 50,
                            height: 50,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Center(
                                child: Text(
                                  _getInitials(conversationModel.title),
                                  style: AppStyles.textSize16(
                                    color: AppColors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              );
                            },
                          ),
                        )
                      : Center(
                          child: conversationModel.users!.length <= 2
                              ? Text(
                                  _getInitials(conversationModel.title),
                                  style: AppStyles.textSize16(
                                    color: AppColors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                )
                              : Icon(
                                  Icons.people,
                                  color: AppColors.white,
                                  size: 24,
                                ),
                        ),
                );
              }),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      children: [
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.only(right: 4),
                            child: Row(
                              children: [
                                Flexible(
                                  child: Text(
                                    title,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    style: AppStyles.textSize16(
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                                BlocBuilder<AuthBloc, AuthState>(
                                    builder: (_, state) {
                                  var id = state.userModel!.docId;
                                  var muted = conversationModel.mutedByUsers
                                      ?.contains(id);
                                  if (muted == true) {
                                    return Icon(
                                      Icons.notifications_off_rounded,
                                      color: AppColors.white,
                                      size: 14,
                                    );
                                  }
                                  return Container();
                                }),
                              ],
                            ),
                          ),
                        ),
                        Column(
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top: 4),
                              child: Text(
                                Helper.formatDateTime(
                                    conversationModel.updatedAt!,
                                    fomat: "dd/MM HH:mm",
                                    isHumanReadable: true),
                                style: AppStyles.textSize12(),
                              ),
                            ),
                            // PopupMenuButton<int>(
                            //   initialValue: 0,
                            //   icon: const Icon(
                            //     Icons.more_horiz,
                            //     color: AppColors.white,
                            //   ),
                            //   color: AppColors.black,
                            //   // Callback that sets the selected popup menu item.
                            //   onSelected: (item) {},
                            //   itemBuilder: (BuildContext context) =>
                            //       <PopupMenuEntry<int>>[
                            //     PopupMenuItem<int>(
                            //       value: 0,
                            //       child: Text(
                            //         'key_mute_notification'.tr(),
                            //         style: AppStyles.textSize14(),
                            //       ),
                            //     ),
                            //     PopupMenuItem<int>(
                            //       value: 0,
                            //       child: Text(
                            //         'key_leave_group'.tr(),
                            //         style: AppStyles.textSize14(),
                            //       ),
                            //     ),
                            //   ],
                            // ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  if (conversationModel.category != null)
                    Container(
                      margin: EdgeInsets.only(bottom: 4),
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      padding: const EdgeInsets.symmetric(
                          vertical: 4, horizontal: 10),
                      child: Text(
                        "${conversationModel.category}",
                        style: AppStyles.textSize12(),
                      ),
                    ),
                  Builder(builder: (context) {
                    return Row(
                      children: [
                        Expanded(
                          child: Builder(builder: (context) {
                            if (containsHtmlTags(
                                "${conversationModel.lastedMessage}")) {
                              return HtmlWidget(
                                  "${conversationModel.lastedMessage}",
                                  customStylesBuilder: (element) {
                                if (element.classes.contains('foo')) {
                                  return {'color': 'red'};
                                }

                                return null;
                              },

                                  // select the render mode for HTML body
                                  // by default, a simple `Column` is rendered
                                  // consider using `ListView` or `SliverList` for better performance
                                  renderMode: RenderMode.column,

                                  // set the default styling for text
                                  textStyle: AppStyles.textSize14());
                            }
                            return Builder(builder: (context) {
                              var type = Helper.getTypeOfMessage(
                                  conversationModel.lastedMessage ?? "");
                              if (type == 'image') {
                                return Row(
                                  children: [
                                    CachedNetworkImage(
                                      imageUrl:
                                          "${conversationModel.lastedMessage}",
                                      width: 25,
                                      height: 25,
                                      fit: BoxFit.cover,
                                    )
                                  ],
                                );
                              }
                              if (type == 'video') {
                                return Row(
                                  children: [
                                    Container(
                                      width: 25,
                                      height: 25,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(4),
                                        color: Colors.blue,
                                      ),
                                      child: Center(
                                        child: Icon(
                                          Icons.video_collection_sharp,
                                          color: Colors.white,
                                          size: 16,
                                        ),
                                      ),
                                    ),
                                  ],
                                );
                              }
                              if (type != null) {
                                return Row(
                                  children: [
                                    Container(
                                      width: 25,
                                      height: 25,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(4),
                                        color: Colors.blue,
                                      ),
                                      child: Center(
                                        child: Icon(
                                          Icons.attachment,
                                          color: Colors.white,
                                          size: 16,
                                        ),
                                      ),
                                    ),
                                  ],
                                );
                              }

                              // if (type == 'image')
                              return Text(
                                "${conversationModel.lastedMessage}",
                                style: AppStyles.textSize14(
                                    color: AppColors.white),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              );
                            });
                          }),
                        ),
                        BlocBuilder<AuthBloc, AuthState>(
                            builder: (context, state) {
                          return StreamBuilder<List<ChatModel>>(
                              stream: FirestoreService.getChatStream(
                                  conversationId: conversationModel.id!,
                                  directoryPath: ""),
                              builder: (_, snapshot) {
                                if (snapshot.data?.isNotEmpty ?? false) {
                                  var count = snapshot.data!
                                      .where((element) =>
                                          (element.readBy!.contains(
                                                  state.userModel?.docId ??
                                                      "") ==
                                              false) &&
                                          (element.senderId !=
                                              (state.userModel?.docId ?? "")))
                                      .length;
                                  if (count > 0) {
                                    return Container(
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: AppColors.primary,
                                      ),
                                      width: 18,
                                      height: 18,
                                      child: Center(
                                        child: Text(
                                          count > 9 ? "9+" : "$count",
                                          style: AppStyles.textSize11(
                                              fontWeight: FontWeight.w500),
                                        ),
                                      ),
                                    );
                                  }
                                }
                                return Container();
                              });
                        })
                      ],
                    );
                  }),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class HeaderAppBar extends StatelessWidget implements PreferredSizeWidget {
  final int badge;
  final UserModel user;
  final Function(int)? onSelected;
  final String? title;
  final String? logo;
  final String? latestMessage;
  final String? latestMessageType;
  final bool isHomework;
  final bool isNotes;
  const HeaderAppBar(
      {super.key,
      required this.badge,
      required this.user,
      this.onSelected,
      this.title,
      this.logo,
      this.latestMessage,
      this.latestMessageType,
      this.isHomework = false,
      this.isNotes = false});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.only(bottom: 0, left: 20, right: 10, top: 0),
        child: Row(
          children: [
            // if (logo?.isNotEmpty ?? false)
            //   Padding(
            //     padding: const EdgeInsets.only(right: 10, top: 10, bottom: 10),
            //     child: ClipRRect(
            //       borderRadius: BorderRadius.circular(8),
            //       child: CachedNetworkImage(
            //         imageUrl: "$logo",
            //         width: 35,
            //         height: 35,
            //         fit: BoxFit.cover,
            //       ),
            //     ),
            //   ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    "Welcome to",
                    style: AppStyles.textSize12(),
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          title ?? "SchoolsMessenger",
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: AppStyles.textSize18(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      // if (latestMessage != null && latestMessage!.isNotEmpty)
                      //   _buildMessageTypeIcon(),
                    ],
                  ),
                  // if (latestMessage != null && latestMessage!.isNotEmpty)
                  //   _buildLatestMessageSubtitle(),
                ],
              ),
            ),
            Builder(builder: (context) {
              return SizedBox(
                width: 35,
                child: IconButton(
                    onPressed: () async {
                      await push(context, Activity());
                    },
                    icon: Stack(
                      children: [
                        const Icon(
                          Icons.notifications_sharp,
                          color: AppColors.primary,
                        ),
                        if (badge > 0)
                          Positioned(
                            top: 0,
                            right: 0,
                            child: Container(
                              padding: EdgeInsets.all(1),
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: AppColors.black,
                              ),
                              child: Container(
                                width: 10,
                                height: 10,
                                decoration: const BoxDecoration(
                                  color: Colors.red,
                                  shape: BoxShape.circle,
                                ),
                                child: Center(
                                  child: Text(
                                    badge > 9 ? "9+" : "$badge",
                                    style: AppStyles.textSize10()
                                        .copyWith(fontSize: 8),
                                  ),
                                ),
                              ),
                            ),
                          ),
                      ],
                    )),
              );
            }),
            SizedBox(
              width: 35,
              child: PopupMenuButton<int>(
                // initialValue: 0,

                icon: Icon(
                  Icons.menu,
                  color: AppColors.primary,
                ),

                color: AppColors.black,
                // Callback that sets the selected popup menu item.
                onSelected: onSelected,
                itemBuilder: (BuildContext context) => <PopupMenuEntry<int>>[
                  // if (user.type == AppConstants.teacherType)
                  //   PopupMenuItem<int>(
                  //     value: 1,
                  //     child: Text(
                  //       'key_search'.tr(),
                  //       style: AppStyles.textSize14(),
                  //     ),
                  //   ),
                  if (user.type == AppConstants.teacherType)
                    PopupMenuItem<int>(
                      value: 0,
                      child: Text(
                        'key_new_group'.tr(),
                        style: AppStyles.textSize14(),
                      ),
                    ),
                  PopupMenuItem<int>(
                    value: 7,
                    child: Text(
                      'key_settings'.tr(),
                      style: AppStyles.textSize14(),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageTypeIcon() {
    IconData iconData;
    Color iconColor;

    if (isHomework) {
      iconData = Icons.assignment_outlined;
      iconColor = Colors.orange;
    } else if (isNotes) {
      iconData = Icons.note_outlined;
      iconColor = Colors.blue;
    } else if (latestMessageType != null && latestMessageType != 'text') {
      iconData = _getMediaIcon();
      iconColor = AppColors.primary;
    } else {
      iconData = Icons.chat_bubble_outline;
      iconColor = AppColors.primary;
    }

    return Container(
      margin: const EdgeInsets.only(left: 8),
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: iconColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: iconColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Icon(
        iconData,
        size: 16,
        color: iconColor,
      ),
    );
  }

  Widget _buildLatestMessageSubtitle() {
    return Container(
      margin: const EdgeInsets.only(top: 2),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.3),
          width: 0.5,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Check if it's a media message
          if (latestMessageType != null && latestMessageType != 'text') ...[
            Container(
              width: 14,
              height: 14,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(2),
                color: AppColors.primary.withValues(alpha: 0.9),
              ),
              child: Icon(
                _getMediaIcon(),
                color: Colors.white,
                size: 10,
              ),
            ),
            const SizedBox(width: 6),
            Flexible(
              child: Text(
                _getMediaTypeText(),
                style: AppStyles.textSize11(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ] else ...[
            // For text messages - check if it's homework or notes
            Icon(
              _getMessageIcon(),
              color: _getMessageIconColor(),
              size: 12,
            ),
            const SizedBox(width: 6),
            Flexible(
              child: Text(
                _getMessageDisplayText(),
                style: AppStyles.textSize11(
                  color: _getMessageTextColor(),
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ],
      ),
    );
  }

  IconData _getMediaIcon() {
    switch (latestMessageType) {
      case 'image':
        return Icons.image_outlined;
      case 'video':
        return Icons.videocam_outlined;
      case 'audio':
        return Icons.audiotrack_outlined;
      case 'document':
      case 'pdf':
        return Icons.description_outlined;
      default:
        return Icons.attach_file_outlined;
    }
  }

  String _getMediaTypeText() {
    switch (latestMessageType) {
      case 'image':
        return 'Photo';
      case 'video':
        return 'Video';
      case 'audio':
        return 'Audio';
      case 'document':
      case 'pdf':
        return 'Document';
      default:
        return 'File';
    }
  }

  IconData _getMessageIcon() {
    if (isHomework) {
      return Icons.assignment_outlined;
    } else if (isNotes) {
      return Icons.note_outlined;
    } else {
      return Icons.chat_bubble_outline;
    }
  }

  Color _getMessageIconColor() {
    if (isHomework) {
      return Colors.orange.withValues(alpha: 0.9);
    } else if (isNotes) {
      return Colors.blue.withValues(alpha: 0.9);
    } else {
      return AppColors.primary.withValues(alpha: 0.8);
    }
  }

  String _getMessageDisplayText() {
    if (isHomework) {
      return 'Homework: ${latestMessage ?? ''}';
    } else if (isNotes) {
      return 'Notes: ${latestMessage ?? ''}';
    } else {
      return latestMessage ?? '';
    }
  }

  Color _getMessageTextColor() {
    if (isHomework) {
      return Colors.orange;
    } else if (isNotes) {
      return Colors.blue;
    } else {
      return AppColors.primary;
    }
  }

  @override
  Size get preferredSize => const Size.fromHeight(65);
}
