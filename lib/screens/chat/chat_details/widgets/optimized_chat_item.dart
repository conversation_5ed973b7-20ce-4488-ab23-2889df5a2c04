import 'dart:io';

import 'package:any_link_preview/any_link_preview.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:chat_app/blocs/auth_bloc/auth_bloc.dart';
import 'package:chat_app/export.dart';
import 'package:chat_app/models/auths/user_model.dart';
import 'package:chat_app/models/chats/conversatoin_model.dart';
import 'package:chat_app/screens/chat/chat_details/widgets/play_image.dart';
import 'package:chat_app/screens/chat/chat_details/widgets/play_video.dart';
import 'package:chat_app/screens/chat/chat_details/widgets/reply_chat_item.dart';
import 'package:chat_app/screens/chat/chat_details/widgets/voice_recording.dart';
import 'package:chat_app/screens/chat/chat_details/widgets/message_indicators.dart';
import 'package:chat_app/screens/chat/chat_details/widgets/message_status_indicator.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/widgets/pdf_thumbnail.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:highlight_text/highlight_text.dart';
import 'package:open_filex/open_filex.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:url_launcher/url_launcher_string.dart';

/// Optimized ChatItem with better structure and cleaner UI
class OptimizedChatItem extends StatelessWidget {
  final UserModel? teacher;
  final Function(int index) onLongPress;
  final Function(ChatModel message, int index) onDownload;
  final String? query;
  final ChatModel chat;
  final int index;
  final Function(ChatModel) onReplyTap;
  final VoidCallback? onHomeworkTap;
  final VoidCallback? onNotesTap;
  final bool showAvatar;
  final Widget? bottomWidget;
  final bool showMarkIcon;
  final bool showMessageIndicators;
  final double? widthItem;
  final Function(ChatModel)? onRetryMessage;

  const OptimizedChatItem({
    super.key,
    this.teacher,
    required this.onLongPress,
    required this.onDownload,
    this.query,
    required this.index,
    required this.chat,
    required this.onReplyTap,
    this.onHomeworkTap,
    this.onNotesTap,
    this.showAvatar = true,
    this.bottomWidget,
    this.showMarkIcon = true,
    this.showMessageIndicators = true,
    this.widthItem,
    this.onRetryMessage,
  });

  @override
  Widget build(BuildContext context) {
    final bool fromMe =
        BlocProvider.of<AuthBloc>(context).state.userModel?.docId ==
            chat.senderId;
    final String teacherName = chat.senderName ?? "";
    final double widthChatItem = widthItem ?? getWidth(context) * 0.7;

    return IgnorePointer(
      ignoring: chat.deletedAt != null,
      child: GestureDetector(
        onLongPress: () {
          onLongPress(index);
        },
        onTap: () {
          if (chat.replyChat != null) {
            onReplyTap(chat.replyChat!);
          }
        },
        child: Container(
          margin: const EdgeInsets.symmetric(vertical: 4),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: fromMe
                ? _buildFromMeLayout(context, teacherName, widthChatItem)
                : _buildFromOtherLayout(context, teacherName, widthChatItem),
          ),
        ),
      ),
    );
  }

  List<Widget> _buildFromMeLayout(
      BuildContext context, String teacherName, double widthChatItem) {
    return [
      // const Spacer(),
      Expanded(
        // flex: 3,
        child: _buildMessageBubble(context, true, teacherName, widthChatItem),
      ),
      if (showAvatar) ...[
        const SizedBox(width: 8),
        _buildAvatar(),
      ],
    ];
  }

  List<Widget> _buildFromOtherLayout(
      BuildContext context, String teacherName, double widthChatItem) {
    return [
      if (showAvatar) ...[
        _buildAvatar(),
        const SizedBox(width: 8),
      ],
      Expanded(
        // flex: 3,
        child: _buildMessageBubble(context, false, teacherName, widthChatItem),
      ),
      // const Spacer(),
    ];
  }

  Widget _buildAvatar() {
    return CircleAvatar(
      radius: 16,
      backgroundColor: AppColors.white.withOpacity(0.2),
      backgroundImage: teacher?.avatar != null
          ? CachedNetworkImageProvider(teacher!.avatar!)
          : null,
      child: teacher?.avatar == null
          ? Icon(Icons.person, color: AppColors.white, size: 16)
          : null,
    );
  }

  Widget _buildMessageBubble(BuildContext context, bool fromMe,
      String teacherName, double widthChatItem) {
    return Column(
      crossAxisAlignment:
          fromMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        Container(
          constraints: BoxConstraints(maxWidth: widthChatItem),
          decoration: BoxDecoration(
            color: fromMe
                ? AppColors.primary.withOpacity(0.1)
                : AppColors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16).copyWith(
              bottomLeft:
                  fromMe ? const Radius.circular(16) : const Radius.circular(4),
              bottomRight:
                  fromMe ? const Radius.circular(4) : const Radius.circular(16),
            ),
            border: Border.all(
              color: AppColors.white.withOpacity(0.1),
              width: 0.5,
            ),
          ),
          child:
              _buildMessageContent(context, fromMe, teacherName, widthChatItem),
        ),
        if (chat.deletedAt == null) _buildTimestamp(fromMe),
      ],
    );
  }

  Widget _buildMessageContent(BuildContext context, bool fromMe,
      String teacherName, double widthChatItem) {
    if (chat.deletedAt != null) {
      return Padding(
        padding: const EdgeInsets.all(8.0),
        child: Text(
          "key_this_message_has_been_delete".tr(),
          style: AppStyles.textSize14().copyWith(
            fontStyle: FontStyle.italic,
            color: AppColors.grey,
          ),
        ),
      );
    }
    return GestureDetector(
      onLongPress: () => onLongPress(index),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (teacherName.isNotEmpty && !fromMe)
              _buildTeacherName(teacherName),
            if (chat.replyChat != null) _buildReplyMessage(),
            _buildMainContent(context, fromMe, teacherName, widthChatItem),
            if (chat.linkButton != null) _buildLinkButton(),
            if (showMessageIndicators) _buildMessageIndicators(fromMe),
            _buildMessageStatus(fromMe),
            if (showMarkIcon && fromMe) _buildReadStatus(),
          ],
        ),
      ),
    );
  }

  Widget _buildTeacherName(String teacherName) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Text(
        teacherName,
        style: AppStyles.textSize12(
          color: AppColors.primary,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildReplyMessage() {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: ReplyChatItem(
        onReplyTap: () => onReplyTap(chat.replyChat!),
        chat: chat.replyChat!,
      ),
    );
  }

  Widget _buildMainContent(BuildContext context, bool fromMe,
      String teacherName, double widthChatItem) {
    if (chat.deletedAt != null) {
      return Text(
        "key_this_message_has_been_delete".tr(),
        style: AppStyles.textSize14().copyWith(
          fontStyle: FontStyle.italic,
          color: AppColors.grey,
        ),
      );
    }

    final String? message = chat.message;
    if (message != null && isUrl(message)) {
      return _buildMediaContent(context, fromMe, teacherName, widthChatItem);
    }

    return _buildTextContent();
  }

  Widget _buildTextContent() {
    if (containsHtmlTags("${chat.message}")) {
      return HtmlWidget(
        "${chat.message}",
        textStyle: AppStyles.textSize14(),
        onTapUrl: (url) => launchUrlString(url),
      );
    }

    if (chat.linkButton != null && isUrl("${chat.linkButton!['link']}")) {
      return _LinkPreviewWidget(
        chat: chat,
        index: index,
        onLongPress: onLongPress,
        link: "${chat.linkButton!['link']}",
      );
    }

    return TextHighlight(
      text: "${chat.message}",
      words: (query?.isNotEmpty ?? false)
          ? {
              "$query": HighlightedWord(
                onTap: () {},
                textStyle: AppStyles.textSize14(),
                decoration: BoxDecoration(
                  color: Colors.yellow[800],
                  borderRadius: BorderRadius.circular(4),
                ),
                padding: const EdgeInsets.symmetric(vertical: 1, horizontal: 2),
              ),
            }
          : {},
      textStyle: AppStyles.textSize14(),
    );
  }

  Widget _buildMediaContent(BuildContext context, bool fromMe,
      String teacherName, double widthChatItem) {
    final String type = chat.type ?? "";

    switch (type) {
      case "image":
        return _ImageMessageWidget(
          chat: chat,
          fromMe: fromMe,
          teacherName: teacherName,
          widthChatItem: widthChatItem,
          index: index,
          onLongPress: onLongPress,
        );
      case "video":
        return _VideoMessageWidget(
          chat: chat,
          fromMe: fromMe,
          teacherName: teacherName,
          widthChatItem: widthChatItem,
          index: index,
          onLongPress: onLongPress,
        );
      case "audio":
        return _AudioMessageWidget(
          chat: chat,
          fromMe: fromMe,
          index: index,
          onLongPress: onLongPress,
        );
      default:
        return _FileMessageWidget(
          chat: chat,
          fromMe: fromMe,
          teacherName: teacherName,
          widthChatItem: widthChatItem,
          index: index,
          onLongPress: onLongPress,
          onDownload: onDownload,
        );
    }
  }

  Widget _buildLinkButton() {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      child: Column(
        children: [
          Divider(height: 16, color: AppColors.white),
          GestureDetector(
            onTap: () => _handleLinkButtonTap(),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.open_in_new,
                    size: 16, color: AppColors.primary),
                const SizedBox(width: 4),
                Text(
                  "${chat.linkButton!['label']}",
                  style: AppStyles.textSize14(
                    fontWeight: FontWeight.w500,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageIndicators(bool fromMe) {
    return MessageIndicators(
      isHomework: chat.isHomework,
      isNotes: chat.isNotes,
      fromMe: fromMe,
      onHomeworkTap: onHomeworkTap,
      onNotesTap: onNotesTap,
      bottomWidget: bottomWidget,
    );
  }

  Widget _buildMessageStatus(bool fromMe) {
    return MessageStatusIndicator(
      isPending: chat.isPending,
      isFailed: chat.isFailed,
      fromMe: fromMe,
      onRetry: () {
        // TODO: Implement retry logic
        if (onRetryMessage != null) {
          onRetryMessage!(chat);
        }
      },
    );
  }

  Widget _buildReadStatus() {
    return Align(
      alignment: Alignment.centerRight,
      child: Padding(
        padding: const EdgeInsets.only(top: 4),
        child: SvgPicture.asset(
          AppAssets.markReadAllIcon,
          color: chat.readAt != null ? AppColors.primary : AppColors.grey,
          width: 14,
        ),
      ),
    );
  }

  Widget _buildTimestamp(bool fromMe) {
    return Padding(
      padding: const EdgeInsets.only(top: 4),
      child: Text(
        Helper.formatDateTime(chat.createdAt!, fomat: "dd/MM/yy hh:mm a"),
        style: AppStyles.textSize10().copyWith(color: AppColors.grey),
      ),
    );
  }

  void _handleLinkButtonTap() async {
    // Implementation for link button tap
    // This is a simplified version - you can expand based on your needs
    final String link = chat.linkButton!['link'];
    try {
      await launchUrl(Uri.parse(link), mode: LaunchMode.externalApplication);
    } catch (e) {
      // Handle error
    }
  }
}

/// Link preview widget for URL messages
class _LinkPreviewWidget extends StatelessWidget {
  final ChatModel chat;
  final int index;
  final Function(int index) onLongPress;
  final String link;

  const _LinkPreviewWidget({
    required this.chat,
    required this.index,
    required this.onLongPress,
    required this.link,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onLongPress: () => onLongPress(index),
      child: AnyLinkPreview(
        link: link,
        displayDirection: UIDirection.uiDirectionHorizontal,
        placeholderWidget: const SizedBox(
          width: 40,
          height: 40,
          child: CupertinoActivityIndicator(),
        ),
        showMultimedia: false,
        bodyMaxLines: 3,
        bodyTextOverflow: TextOverflow.ellipsis,
        titleStyle: AppStyles.textSize16(color: AppColors.primary),
        bodyStyle: AppStyles.textSize14(),
        errorBody: "A preview for this link is not available",
        errorWidget: GestureDetector(
          onTap: () => launchUrlString(link),
          child: Text(
            link,
            style: AppStyles.textSize14().copyWith(
              fontStyle: FontStyle.italic,
              color: AppColors.primary,
              decoration: TextDecoration.underline,
            ),
          ),
        ),
        backgroundColor: Colors.transparent,
        borderRadius: 8,
        removeElevation: true,
        onTap: () => launchUrlString(link),
      ),
    );
  }
}

/// Image message widget
class _ImageMessageWidget extends StatelessWidget {
  final ChatModel chat;
  final bool fromMe;
  final String teacherName;
  final double widthChatItem;
  final int index;
  final Function(int index) onLongPress;

  const _ImageMessageWidget({
    required this.chat,
    required this.fromMe,
    required this.teacherName,
    required this.widthChatItem,
    required this.index,
    required this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        push(
          context,
          PlayImage(
            imageUrl: chat.message!,
            localPath: chat.localPath,
          ),
        );
      },
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Column(
          children: [
            Hero(
              tag: chat.message!,
              child: CachedNetworkImage(
                imageUrl: chat.message!,
                width: widthChatItem * 0.8,
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  width: widthChatItem * 0.8,
                  height: 200,
                  color: AppColors.grey.withOpacity(0.3),
                  child: const Center(child: CupertinoActivityIndicator()),
                ),
                errorWidget: (context, url, error) => Container(
                  width: widthChatItem * 0.8,
                  height: 200,
                  color: AppColors.grey.withOpacity(0.3),
                  child: Icon(Icons.error, color: AppColors.white),
                ),
              ),
            ),
            if (chat.fileContent?.isNotEmpty ?? false)
              Container(
                width: widthChatItem * 0.8,
                padding: const EdgeInsets.all(8),
                color: AppColors.black.withOpacity(0.7),
                child: Text(
                  chat.fileContent!,
                  style: AppStyles.textSize12(),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// Video message widget
class _VideoMessageWidget extends StatelessWidget {
  final ChatModel chat;
  final bool fromMe;
  final String teacherName;
  final double widthChatItem;
  final int index;
  final Function(int index) onLongPress;

  const _VideoMessageWidget({
    required this.chat,
    required this.fromMe,
    required this.teacherName,
    required this.widthChatItem,
    required this.index,
    required this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // Navigate to video player
        push(
          context,
          PlayVideo(
            videoUrl: chat.message!,
            localPath: chat.localPath,
          ),
        );
      },
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Stack(
          children: [
            CachedNetworkImage(
              imageUrl: chat.fileThumbnail ?? "",
              width: widthChatItem * 0.8,
              height: 200,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                width: widthChatItem * 0.8,
                height: 200,
                color: AppColors.grey.withOpacity(0.3),
                child: const Center(child: CupertinoActivityIndicator()),
              ),
            ),
            Positioned.fill(
              child: Container(
                color: Colors.black.withOpacity(0.3),
                child: const Center(
                  child: Icon(
                    Icons.play_circle_fill_rounded,
                    color: Colors.white,
                    size: 50,
                  ),
                ),
              ),
            ),
            if (chat.fileContent?.isNotEmpty ?? false)
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  color: AppColors.black.withOpacity(0.7),
                  child: Text(
                    chat.fileContent!,
                    style: AppStyles.textSize12(),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// Audio message widget
class _AudioMessageWidget extends StatelessWidget {
  final ChatModel chat;
  final bool fromMe;
  final int index;
  final Function(int index) onLongPress;

  const _AudioMessageWidget({
    required this.chat,
    required this.fromMe,
    required this.index,
    required this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    if (chat.message?.isEmpty ?? true) {
      return Container();
    }

    if (kIsWeb) {
      return GestureDetector(
        onTap: () => launchUrlString(chat.message!),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.primary.withOpacity(0.3)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(
                  Icons.headphones,
                  color: AppColors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Audio File",
                      style: AppStyles.textSize14(fontWeight: FontWeight.w500),
                    ),
                    Text(
                      "Tap to play",
                      style: AppStyles.textSize12().copyWith(
                        color: AppColors.grey,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }

    return WaveBubble(
      key: Key(chat.message!),
      isSender: fromMe,
      width: getWidth(context) * 0.6,
      networkPath: chat.message,
    );
  }
}

/// File message widget
class _FileMessageWidget extends StatelessWidget {
  final ChatModel chat;
  final bool fromMe;
  final String teacherName;
  final double widthChatItem;
  final int index;
  final Function(int index) onLongPress;
  final Function(ChatModel message, int index) onDownload;

  const _FileMessageWidget({
    required this.chat,
    required this.fromMe,
    required this.teacherName,
    required this.widthChatItem,
    required this.index,
    required this.onLongPress,
    required this.onDownload,
  });

  @override
  Widget build(BuildContext context) {
    if (isUrl(chat.message ?? "") &&
        Helper.getTypeOfMessage(chat.message ?? "") == null) {
      return _LinkPreviewWidget(
        chat: chat,
        index: index,
        onLongPress: onLongPress,
        link: chat.message!,
      );
    }

    if (chat.fileSize == null) {
      return GestureDetector(
        onTap: () => launchUrlString(chat.message!),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.primary.withOpacity(0.3)),
          ),
          child: Text(
            "${chat.message}",
            style: AppStyles.textSize14().copyWith(
              fontStyle: FontStyle.italic,
              color: AppColors.primary,
              decoration: TextDecoration.underline,
            ),
          ),
        ),
      );
    }

    return GestureDetector(
      onTap: () {
        if (chat.localPath != null) {
          OpenFilex.open(chat.localPath!);
        } else {
          onDownload(chat, index);
        }
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.white.withOpacity(0.05),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppColors.white.withOpacity(0.1)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (_isPdfFile()) _buildPdfPreview(context),
            _buildFileInfo(),
            if (chat.fileContent?.isNotEmpty ?? false) _buildFileContent(),
          ],
        ),
      ),
    );
  }

  bool _isPdfFile() {
    return chat.message?.split(".").last.startsWith("pdf") ?? false;
  }

  Widget _buildPdfPreview(BuildContext context) {
    return FutureBuilder<File>(
      future: DefaultCacheManager().getSingleFile(chat.message!),
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          return Container(
            margin: const EdgeInsets.only(bottom: 12),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: PdfThumbnail.fromFile(
                snapshot.data!.path,
                currentPage: 1,
                height: 150,
                backgroundColor: Colors.transparent,
                scrollToCurrentPage: false,
              ),
            ),
          );
        }
        return Container(
          height: 150,
          margin: const EdgeInsets.only(bottom: 12),
          decoration: BoxDecoration(
            color: AppColors.grey.withOpacity(0.3),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Center(child: CupertinoActivityIndicator()),
        );
      },
    );
  }

  Widget _buildFileInfo() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: SvgPicture.asset(
            AppAssets.attachmentIcon,
            color: AppColors.primary,
            width: 20,
            height: 20,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                chat.fileName ?? "Unknown file",
                style: AppStyles.textSize14(fontWeight: FontWeight.w500),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              if (chat.fileSize != null)
                Text(
                  Helper.formatFileSize(chat.fileSize!),
                  style: AppStyles.textSize12().copyWith(color: AppColors.grey),
                ),
            ],
          ),
        ),
        _buildActionIcon(),
      ],
    );
  }

  Widget _buildActionIcon() {
    if (chat.localPath != null) {
      return Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: AppColors.primary.withOpacity(0.2),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Icon(
          Icons.folder_open,
          size: 20,
          color: AppColors.primary,
        ),
      );
    }
    return Icon(
      Icons.download,
      color: AppColors.primary,
      size: 24,
    );
  }

  Widget _buildFileContent() {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: AppColors.black.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        chat.fileContent!,
        style: AppStyles.textSize12(),
      ),
    );
  }
}
