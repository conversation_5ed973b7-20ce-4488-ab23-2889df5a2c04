import 'package:chat_app/blocs/auth_bloc/auth_bloc.dart';
import 'package:chat_app/models/auths/user_model.dart';
import 'package:chat_app/utils/app_asset.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/app_styles.dart';
import 'package:chat_app/utils/extensions.dart';
import 'package:chat_app/utils/firestore_service.dart';
import 'package:chat_app/utils/helper.dart';
import 'package:chat_app/utils/navigator_service.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:image_picker/image_picker.dart';

class ChatAttachmentModal extends StatelessWidget {
  final UserModel? userModel;
  const ChatAttachmentModal({super.key, this.userModel});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20, vertical: 20),
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(5),
          topRight: Radius.circular(5),
        ),
        color: AppColors.black,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (!kIsWeb)
            GestureDetector(
              onTap: () async {
                var result =
                    await ImagePicker().pickImage(source: ImageSource.camera);
                if (context.mounted) {
                  pop(context,
                      result: result != null
                          ? FilePickerResult([
                              PlatformFile(
                                  path: result.path,
                                  name: result.path,
                                  size: await result.length())
                            ])
                          : null);
                }
              },
              child: Item(
                title: "Camera",
                iconPath: AppAssets.cameraIcon,
              ),
            ),
          Divider(
            height: 10,
          ),
          GestureDetector(
            onTap: () async {
              var result = ImagePicker().pickMultiImage();
              if (context.mounted) {
                pop(context, result: result);
              }
            },
            child: Item(
              title: "key_image".tr(),
              iconPath: AppAssets.imageIcon,
            ),
          ),
          Divider(
            height: 10,
          ),
          // GestureDetector(
          //   onTap: () async {
          //     var result = await FilePicker.platform.pickFiles(
          //       allowMultiple: false,
          //       type: FileType.video,
          //     );
          //     if (context.mounted) {
          //       pop(context, result: result);
          //     }
          //   },
          //   child: Item(
          //     title: "key_video".tr(),
          //     iconPath: AppAssets.videoIcon,
          //   ),
          // ),
          // Divider(
          //   height: 10,
          // ),
          GestureDetector(
            onTap: () async {
              var result = await FilePicker.platform.pickFiles(
                allowMultiple: false,
                type: FileType.custom,
                allowedExtensions: [
                  'txt',
                  'doc',
                  'docx',
                  'pdf',
                  'xls',
                  'xlsx',
                  'ppt',
                  'pptx',
                  'odt',
                  'ods',
                  'odp',
                  'rtf',
                  'tex',
                  'epub',
                  'mobi',
                  'zip'
                ],
              );
              if (context.mounted) {
                pop(context, result: result);
              }
            },
            child: Item(
              title: "key_document".tr(),
              iconPath: AppAssets.documentIcon,
            ),
          ),
          Divider(
            height: 10,
          ),
          GestureDetector(
            onTap: () async {
              var result = await showDialog(
                  context: context,
                  builder: (_) {
                    return const LinkInput();
                  });
              if (context.mounted) {
                pop(context, result: result);
              }
            },
            child: Item(
              title: "key_link".tr(),
              iconPath: AppAssets.linkIcon,
            ),
          ),
          Divider(
            height: 10,
          ),
          if (userModel != null)
            GestureDetector(
              onTap: () async {
                // var randomNumber = Helper.getRandomNumber();
                // var studentId = Helper.getStudentIdFromSid(userModel!.sid!);
                var schoolCode = FirestoreService.projectName.toLowerCase();

                var phoneNumber = userModel?.phoneNumber;
                // var paymentLink =
                //     'upi://pay?pa=EASYBILLPAYSURENA@ICICI&pn=${BlocProvider.of<AuthBloc>(context).state.schoolName}&tr=${schoolCode}_${studentId}_$randomNumber&am=0&cu=INR&mc=6012&tn=${schoolCode}_${studentId}_$randomNumber';
                var paymentLink =
                    "https://$schoolCode.feepayindia.in/opay/index.php?mobileNo=$phoneNumber";
                print("payment link: $paymentLink");
                if (context.mounted) {
                  pop(context, result: paymentLink);
                }
              },
              child: Item(
                title: "key_payment_link".tr(),
                iconPath: AppAssets.linkIcon,
              ),
            ),
        ],
      ),
    );
  }
}

class Item extends StatelessWidget {
  final String title;
  final String iconPath;
  final Icon? icon;
  const Item(
      {super.key, required this.title, required this.iconPath, this.icon});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.transparent,
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.white.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: icon ??
                  SvgPicture.asset(
                    iconPath,
                    color: AppColors.primary,
                  ),
            ),
          ),
          10.w,
          Text(
            title,
            style: AppStyles.textSize14(),
          )
        ],
      ),
    );
  }
}

class LinkInput extends StatefulWidget {
  const LinkInput({super.key});

  @override
  State<LinkInput> createState() => _LinkInputState();
}

class _LinkInputState extends State<LinkInput> {
  final TextEditingController _textEditingController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Dialog(
        backgroundColor: AppColors.backgroundScaffold,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20)
              .copyWith(bottom: 0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: _textEditingController,
                style: AppStyles.textSize14(),
                decoration: InputDecoration(
                  hintText: "key_add_link".tr(),
                  hintStyle: AppStyles.textSize14(
                    color: AppColors.grey,
                  ),
                  border: UnderlineInputBorder(),
                ),
              ),
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                        onPressed: () {
                          pop(context);
                        },
                        child: Text(
                          "key_cancel".tr(),
                          style: AppStyles.textSize14(color: AppColors.grey),
                        )),
                  ),
                  Expanded(
                    child: TextButton(
                        onPressed: () {
                          if (isUrl(_textEditingController.text.trim())) {
                            pop(context,
                                result: _textEditingController.text.trim());
                          } else {
                            showToast("Link is invalid");
                          }
                        },
                        child: Text(
                          "key_add".tr(),
                        )),
                  ),
                ],
              )
            ],
          ),
        ));
  }
}
