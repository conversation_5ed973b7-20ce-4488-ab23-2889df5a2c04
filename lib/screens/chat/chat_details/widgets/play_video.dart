// import 'dart:io';

// import 'package:appinio_video_player/appinio_video_player.dart';
// import 'package:chat_app/models/chats/conversatoin_model.dart';
// import 'package:chat_app/utils/app_colors.dart';
// import 'package:firebase_storage/firebase_storage.dart';

// import 'package:flutter/material.dart';
// import 'package:open_filex/open_filex.dart';
// import 'package:path_provider/path_provider.dart';

// import '../../../../export.dart';
// import '../../../../utils/firestore_service.dart';

// class PlayVideo extends StatefulWidget {
//   final String videoUrl;
//   final String? localPath;
//   const PlayVideo({Key? key, required this.videoUrl, this.localPath})
//       : super(key: key);

//   @override
//   State<PlayVideo> createState() => _PlayVideoState();
// }

// class _PlayVideoState extends State<PlayVideo> {
//   late CachedVideoPlayerController _videoPlayerController;

//   CustomVideoPlayerController? _customVideoPlayerController;
//   final CustomVideoPlayerSettings _customVideoPlayerSettings =
//       const CustomVideoPlayerSettings(showSeekButtons: true);
//   bool _isLoading = false;
//   String? _localPath;

//   @override
//   void initState() {
//     super.initState();
//     _localPath = widget.localPath;
//     if (_localPath != null) {
//       _videoPlayerController =
//           CachedVideoPlayerController.file(File(_localPath!));
//     } else {
//       _videoPlayerController = CachedVideoPlayerController.network(
//         widget.videoUrl,
//       );
//     }
//     _videoPlayerController.initialize().then((value) => setState(() {}));
//     _customVideoPlayerController = CustomVideoPlayerController(
//       context: context,
//       videoPlayerController: _videoPlayerController,
//       customVideoPlayerSettings: _customVideoPlayerSettings,
//     );
//     _customVideoPlayerController?.videoPlayerController.play();
//   }

//   _onDownload(String link) async {
//     try {
//       setState(() {
//         _isLoading = true;
//       });

//       final directory = await getApplicationDocumentsDirectory();

//       // if (await file.exists()) {
//       //   file.delete();
//       // }
//       String url = link;

//       // Use Uri.parse() to parse the URL
//       Uri uri = Uri.parse(url);

//       // Extract the 'path' component from the URI
//       String path = uri.path;

//       path = path.split('/').last;

//       path = Uri.decodeComponent(path);

//       File file = File('${directory!.path}/$path');
//       file.create(recursive: true);
//       Reference storageReference = FirestoreService.storageRef.child(path);
//       await storageReference.writeToFile(file);

//       setState(() {
//         _isLoading = false;
//         _localPath = file.path;
//       });

//       OpenFilex.open(file.path);
//     } catch (e) {
//       setState(() {
//         _isLoading = false;
//       });
//       showToast(e.toString());
//     }
//   }

//   @override
//   void dispose() {
//     _customVideoPlayerController?.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return OverlayLoading(
//       isLoading: _isLoading,
//       child: Scaffold(
//         backgroundColor: AppColors.backgroundScaffold,
//         appBar: CustomAppbar(
//           title: "",
//           actions: [
//             IconButton(
//               onPressed: () async {
//                 if (_localPath != null) {
//                   OpenFilex.open(_localPath);
//                 } else {
//                   _customVideoPlayerController?.videoPlayerController.pause();
//                   await _onDownload(widget.videoUrl);
//                 }
//               },
//               icon: _localPath != null
//                   ? Icon(
//                       Icons.folder,
//                       color: AppColors.white,
//                     )
//                   : Icon(
//                       Icons.download,
//                       color: AppColors.white,
//                     ),
//             )
//           ],
//         ),
//         body: SafeArea(child: Center(
//           child: Builder(builder: (context) {
//             if (_customVideoPlayerController != null) {
//               return CustomVideoPlayer(
//                 customVideoPlayerController: _customVideoPlayerController!,
//               );
//             }
//             return Center(
//               child: CircularProgressIndicator(),
//             );
//           }),
//         )),
//       ),
//     );
//   }
// }
