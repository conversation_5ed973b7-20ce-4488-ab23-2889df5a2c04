import 'dart:io';

import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:chat_app/blocs/auth_bloc/auth_bloc.dart';
import 'package:chat_app/models/auths/user_model.dart';
import 'package:chat_app/models/chats/conversatoin_model.dart';
import 'package:chat_app/screens/chat/chat_details/delivered_status.dart';
import 'package:chat_app/screens/chat/chat_details/view_medias_and_links.dart';
import 'package:chat_app/screens/chat/chat_details/view_members.dart';
import 'package:chat_app/screens/chat/chat_details/widgets/change_room_dialog.dart';
import 'package:chat_app/screens/chat/chat_details/widgets/forward_message_modal.dart';
import 'package:chat_app/screens/chat/chat_details/widgets/search_message_input.dart';
import 'package:chat_app/screens/chat/chat_details/widgets/homework_notes_navigation.dart';
import 'package:chat_app/screens/chat/chat_details/widgets/voice_recording.dart';
import 'package:chat_app/screens/chat/search/search_parent.dart';
import 'package:chat_app/utils/app_colors.dart';
import 'package:chat_app/utils/app_constants.dart';
import 'package:chat_app/utils/app_styles.dart';
import 'package:chat_app/utils/firestore_service.dart';
import 'package:chat_app/utils/helper.dart';
import 'package:chat_app/utils/navigator_service.dart';
import 'package:chat_app/utils/platform_helper.dart';
import 'package:chat_app/widgets/overlay_loading.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mime_type/mime_type.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'package:share_plus/share_plus.dart';

import 'widgets/chat_input.dart';
import 'widgets/list_conversations.dart';
import 'widgets/message_options_modal.dart';
import 'widgets/teacher_filter_dialog.dart';

class ChatDetails extends StatefulWidget {
  final bool isNewMessage;
  final String? converationId;
  final ConversationModel? conversationModel;
  final List<UserModel> initUsers;
  final String? roomName;
  final String? image;
  final String? jumpToMessageId;
  final bool showHomeworkNotesNavigation;
  final String? initialNavigationType;

  const ChatDetails(
      {super.key,
      this.isNewMessage = false,
      this.converationId,
      this.conversationModel,
      this.initUsers = const [],
      this.roomName,
      this.image,
      this.jumpToMessageId,
      this.showHomeworkNotesNavigation = false,
      this.initialNavigationType});

  @override
  State<ChatDetails> createState() => _ChatDetailsState();
}

class _ChatDetailsState extends State<ChatDetails> {
  final TextEditingController _inputController = TextEditingController();
  final TextEditingController _searchMessageController =
      TextEditingController();

  final FocusNode _focusNode = FocusNode();
  List<UserModel> _users = [];
  List<ChatModel> _chats = [];
  Stream<List<ChatModel>>? _chatStream;
  String? _conversationId;
  UserModel? _teacherInfo;
  bool _isLoading = false;
  String? _tempLink;
  String _roomName = '';
  final ScrollController _scrollController = ScrollController();
  Directory? _directory;
  ConversationModel? _conversationModel;
  bool _showSearch = false;
  String? _query;
  Stream<ConversationModel>? _conversationStream;

  // Filter functionality
  String? _selectedTeacherId;
  bool _showFilterFAB = true;

  final ItemScrollController itemScrollController = ItemScrollController();
  final ScrollOffsetController scrollOffsetController =
      ScrollOffsetController();
  final ItemPositionsListener itemPositionsListener =
      ItemPositionsListener.create();
  final ScrollOffsetListener scrollOffsetListener =
      ScrollOffsetListener.create();
  List<File> _tempFiles = [];
  List<PlatformFile> _tempPlatformFiles = [];
  String? _imageGroup;
  ChatModel? _replyModel;
  String? path;
  String? musicFile;
  bool isRecording = false;
  bool isRecordingCompleted = false;
  bool isLoading = true;
  late Directory appDirectory;

  // Homework/Notes navigation state
  bool _showHomeworkNotesNavigation = false;
  NavigationType _currentNavigationType = NavigationType.homework;
  int _currentNavigationIndex = 0;
  List<ChatModel> _homeworkMessages = [];
  List<ChatModel> _notesMessages = [];

  @override
  void initState() {
    FirestoreService.currentConversationId = widget.converationId;
    _conversationModel = widget.conversationModel;
    if (widget.roomName != null) {
      _roomName = widget.roomName!;
    }
    if (widget.image != null) {
      _imageGroup = widget.image;
    }
    if (widget.conversationModel != null) {
      _roomName = widget.conversationModel?.title ?? "";
      bool isGroup = widget.conversationModel!.userIds!.length > 2;

      // Show filter FAB only for group chats
      _showFilterFAB = true;

      var user = BlocProvider.of<AuthBloc>(context).state.userModel;
      if (isGroup == false) {
        try {
          _roomName = widget.conversationModel!.users!
                  .firstWhere((element) => element.docId != user?.docId)
                  .name ??
              "";
          if (user?.type == AppConstants.parentType) {
            _roomName = widget.conversationModel!.users!
                    .firstWhere((element) =>
                        user!.students!.contains(element.docId) == false)
                    .name ??
                "";
          }
        } catch (e) {}
      }
    } else {
      _initChatWithInitUsers();
    }
    _focusNode.addListener(() async {
      setState(() {});
      if (_focusNode.hasFocus) {
        _scrollToBottom();
      }
    });
    if (widget.converationId != null) {
      _conversationId = widget.converationId;
      _initChat(conversationId: widget.converationId!);
    }
    _getTeacherInfo();
    _getDirectory();

    super.initState();
  }

  _markReadAllMessages(List<ChatModel> chats) async {
    if (mounted) {
      var user = BlocProvider.of<AuthBloc>(context).state.userModel!;
      var list = chats
          .where((element) =>
              element.readBy!.contains(user.docId) == false &&
              element.senderId != user.docId)
          .toList();
      if (list.isNotEmpty) {
        await FirestoreService.markReadAllMessage(
            messages: list,
            conversationId: _conversationId!,
            model: _conversationModel!);
      }
    }
  }

  _initChatWithInitUsers() {
    if (widget.initUsers.isNotEmpty) {
      _users = widget.initUsers.toList();
      _roomName = _users
          .map((e) => e.name ?? "")
          .reduce((value, element) => value + " , " + element);
      if (widget.roomName != null) {
        _roomName = widget.roomName!;
      }
      setState(() {});
      _checkConversationExist();
    }
  }

  _getDirectory() async {
    if (kIsWeb) {
      // On web, we can't access file system
      _directory = null;
    } else {
      _directory =
          await PlatformHelper.getApplicationDocumentsDirectoryCompat();
    }
  }

  _getTeacherInfo() async {
    if (widget.conversationModel != null) {
      _teacherInfo = await FirestoreService.getUserDetails(
          docId: widget.conversationModel!.teacherId!);
    } else if (BlocProvider.of<AuthBloc>(context).state.userModel?.type ==
        AppConstants.teacherType) {
      _teacherInfo = BlocProvider.of<AuthBloc>(context).state.userModel;
    }
    if (mounted) {
      setState(() {});
    }
  }

  _initChat({required String conversationId}) async {
    if (_directory == null) {
      await _getDirectory();
    }

    String directoryPath = "";
    if (_directory != null) {
      directoryPath = _directory!.path;
    } else if (kIsWeb) {
      // On web, use a placeholder path since we can't access file system
      directoryPath = "/web_cache";
    }

    _chatStream = FirestoreService.getChatStream(
        conversationId: conversationId, directoryPath: directoryPath);
    _chatStream!.listen((event) async {
      if (mounted) {
        setState(() {
          // Merge server messages with optimistic messages
          final serverMessages = event.toList().reversed.toList();
          final optimisticMessages = _chats
              .where((chat) => chat.isPending == true || chat.isFailed == true)
              .toList();

          // Remove optimistic messages that now exist on server
          final filteredOptimistic = optimisticMessages.where((optimistic) {
            bool hasMatch = serverMessages
                .any((server) => _isMatchingMessage(optimistic, server));

            // Debug logging
            if (hasMatch) {
              print(
                  "🔄 Removing optimistic message: ${optimistic.fileName ?? optimistic.message}");
            }

            return !hasMatch;
          }).toList();

          // Debug logging
          if (optimisticMessages.length != filteredOptimistic.length) {
            print(
                "📝 Optimistic messages: ${optimisticMessages.length} → ${filteredOptimistic.length}");
          }

          // Combine server messages with remaining optimistic messages
          _chats = [...filteredOptimistic, ...serverMessages];
        });
        _updateHomeworkNotesLists();

        // Handle jump to specific message if requested
        if (widget.jumpToMessageId != null && _chats.isNotEmpty) {
          _handleJumpToMessage();
        }
      }
      _markReadAllMessages(event);

      await Future.delayed(Duration(milliseconds: 100));
      if (_chats.isNotEmpty && widget.jumpToMessageId == null) {
        _scrollToBottom();
      }
    });
    if (_conversationStream == null) {
      _conversationStream =
          FirestoreService.getConversationDetailsStream(id: conversationId);
      _conversationStream!.listen((event) {
        if (mounted) {
          setState(() {
            _conversationModel = event;
          });
        }
      });
    }
  }

  Future<String> _getConversationId({required String message}) async {
    var senderId = BlocProvider.of<AuthBloc>(context).state.userModel!.docId!;
    String title = _roomName;
    if (_conversationId == null || _conversationModel == null) {
      var model = await FirestoreService.createConversation(
          teacherId: senderId,
          users: _users,
          lastedMessage: message,
          image: _imageGroup,
          title: title);
      _conversationModel = model;
      _conversationId = model.id;
    }

    if (_chatStream == null) {
      _initChat(conversationId: _conversationId!);
    }
    return _conversationId!;
  }

  _onSendMessage({String? htmlText}) async {
    if (_inputController.text.isNotEmpty ||
        htmlText != null ||
        _tempLink != null) {
      hideKeyboard(context);
      var tempReply = _replyModel;
      setState(() {
        _replyModel = null;
      });
      String text = htmlText ?? _inputController.text;
      _inputController.clear();

      var senderId = BlocProvider.of<AuthBloc>(context).state.userModel!.docId!;
      var senderName = getProfile(context).name ?? "";

      await _getConversationId(message: text);

      bool isYoutube = false;
      Map<String, dynamic>? linkButton;
      if (_tempLink != null) {
        var uri = Uri.parse(_tempLink!);
        if (uri.host.contains("youtube") || uri.host.contains("youtu.be")) {
          isYoutube = true;
        }
        linkButton = {
          "link": _tempLink,
          "label": _tempLink!.startsWith("upi") ||
                  _tempLink!.contains("feepayindia.in/opay/index.php")
              ? "Pay Now"
              : isYoutube
                  ? "Youtube link"
                  : "Open Link"
        };
      }

      // Create optimistic message for immediate UI display
      final optimisticMessage = ChatModel.createOptimistic(
        message: text,
        senderId: senderId,
        senderName: senderName,
        type: "text",
        linkButton: linkButton,
        replyChat: tempReply,
      );

      // Add optimistic message to UI immediately
      setState(() {
        _chats.insert(0, optimisticMessage);
        if (_tempLink != null) {
          _tempLink = null;
        }
      });

      // Scroll to bottom to show new message
      _scrollToBottom();

      // Send message to server in background
      try {
        if (mounted) {
          await FirestoreService.sendMessage(
              replyChat: tempReply,
              conversationModel: _conversationModel!,
              senderName: senderName,
              linkButton: linkButton,
              message: text,
              senderId: senderId);
        }
      } catch (e) {
        // Mark message as failed if sending fails
        if (mounted) {
          setState(() {
            final index = _chats
                .indexWhere((chat) => chat.tempId == optimisticMessage.tempId);
            if (index != -1) {
              _chats[index] = _chats[index].copyWith(
                isPending: false,
                isFailed: true,
              );
            }
          });
        }
        showToast("Failed to send message: ${e.toString()}");
      }
    }
  }

  /// Retry sending a failed message
  _onRetryMessage(ChatModel failedMessage) async {
    if (failedMessage.tempId == null) return;

    // Mark message as pending again
    setState(() {
      final index =
          _chats.indexWhere((chat) => chat.tempId == failedMessage.tempId);
      if (index != -1) {
        _chats[index] = _chats[index].copyWith(
          isPending: true,
          isFailed: false,
        );
      }
    });

    try {
      if (failedMessage.type == "text") {
        await FirestoreService.sendMessage(
          replyChat: failedMessage.replyChat,
          conversationModel: _conversationModel!,
          senderName: failedMessage.senderName ?? "",
          linkButton: failedMessage.linkButton,
          message: failedMessage.message ?? "",
          senderId: failedMessage.senderId ?? "",
        );
      } else if (['image', 'video', 'file'].contains(failedMessage.type)) {
        // Handle media retry
        if (failedMessage.fileContent != null) {
          // Try to re-upload the file
          File file = File(failedMessage.fileContent!);
          if (await file.exists()) {
            var task = await FirestoreService.uploadFileToFireStorage(file);
            await task.whenComplete(() {});

            String path = await task.storage
                .ref(task.snapshot.metadata!.fullPath)
                .getDownloadURL();

            await FirestoreService.sendMediaMessage(
              replyChat: failedMessage.replyChat,
              conversationModel: _conversationModel!,
              fileThumbnail: null,
              path: path,
              senderId: failedMessage.senderId ?? "",
              type: failedMessage.type ?? "file",
              fileName: failedMessage.fileName ?? "file",
              fileSize: failedMessage.fileSize ?? 0,
              content: failedMessage.message ?? "",
            );
          } else {
            throw Exception("Original file not found");
          }
        } else {
          throw Exception("No file content available for retry");
        }
      } else {
        showToast("Retry not supported for this message type");
      }
    } catch (e) {
      // Mark as failed again
      if (mounted) {
        setState(() {
          final index =
              _chats.indexWhere((chat) => chat.tempId == failedMessage.tempId);
          if (index != -1) {
            _chats[index] = _chats[index].copyWith(
              isPending: false,
              isFailed: true,
            );
          }
        });
      }
      showToast("Failed to retry message: ${e.toString()}");
    }
  }

  _onSendMedia(File result) async {
    hideKeyboard(context);

    var file = result;
    var type = mime(file.path)!.split('/').first;
    var senderId = BlocProvider.of<AuthBloc>(context).state.userModel!.docId!;
    var senderName = getProfile(context).name ?? "";
    var tempReply = _replyModel;
    var tempContent = _inputController.text;

    // Clear UI state immediately
    setState(() {
      _replyModel = null;
      _tempFiles.clear();
      _tempPlatformFiles.clear();
    });
    _inputController.clear();

    await _getConversationId(message: file.path);

    // Compress image if needed
    if (type == 'image') {
      var compressPath = await Helper.compressImage(file.path);
      file = File(compressPath);
    }

    int fileSize = await file.length();
    String fileName = file.path.split('/').last;

    // Create optimistic media message with local file path
    final optimisticMessage = ChatModel.createOptimistic(
      message: tempContent.isNotEmpty ? tempContent : fileName,
      senderId: senderId,
      senderName: senderName,
      type: type,
      fileName: fileName,
      fileSize: fileSize,
      fileContent: file.path, // Use local path for immediate display
      replyChat: tempReply,
    );

    // Add optimistic message to UI immediately
    setState(() {
      _chats.insert(0, optimisticMessage);
    });

    // Scroll to show new message
    _scrollToBottom();

    // Upload file and send message in background
    try {
      var task = await FirestoreService.uploadFileToFireStorage(file);
      await task.whenComplete(() {});

      String? fileThumbnail;
      if (type == 'video') {
        // Video thumbnail logic can be added here if needed
      }

      String path = await task.storage
          .ref(task.snapshot.metadata!.fullPath)
          .getDownloadURL();

      await FirestoreService.sendMediaMessage(
        replyChat: tempReply,
        conversationModel: _conversationModel!,
        fileThumbnail: fileThumbnail,
        path: path,
        senderId: senderId,
        type: type,
        fileName: fileName,
        fileSize: fileSize,
        content: tempContent,
      );
    } catch (e) {
      // Mark message as failed if upload/send fails
      if (mounted) {
        setState(() {
          final index = _chats
              .indexWhere((chat) => chat.tempId == optimisticMessage.tempId);
          if (index != -1) {
            _chats[index] = _chats[index].copyWith(
              isPending: false,
              isFailed: true,
            );
          }
        });
      }
      showToast("Failed to send media: ${e.toString()}");
    }
  }

  /// Check if optimistic message matches server message
  bool _isMatchingMessage(ChatModel optimistic, ChatModel server) {
    // Must have same sender and type
    if (optimistic.senderId != server.senderId ||
        optimistic.type != server.type) {
      return false;
    }

    // For text messages, compare message content
    if (optimistic.type == 'text') {
      return optimistic.message == server.message;
    }

    // For media messages, compare by filename and file size
    if (['image', 'video', 'file'].contains(optimistic.type)) {
      // Check if filename and file size match
      bool filenameMatch = optimistic.fileName == server.fileName;
      bool fileSizeMatch = optimistic.fileSize == server.fileSize;

      // Also check if they were sent within a reasonable time window (5 minutes)
      if (optimistic.createdAt != null && server.createdAt != null) {
        try {
          DateTime optimisticTime = DateTime.parse(optimistic.createdAt!);
          DateTime serverTime = DateTime.parse(server.createdAt!);
          Duration timeDiff = serverTime.difference(optimisticTime).abs();
          bool timeMatch = timeDiff.inMinutes <= 5;

          return filenameMatch && fileSizeMatch && timeMatch;
        } catch (e) {
          // If date parsing fails, just use filename and size
          return filenameMatch && fileSizeMatch;
        }
      }

      return filenameMatch && fileSizeMatch;
    }

    return false;
  }

  /// Get list of teachers/users in the group chat
  List<UserModel> _getGroupTeachers() {
    if (_conversationModel?.users != null) {
      return _conversationModel!.users!.where((user) {
        // Filter out current user and students if needed
        var currentUser = BlocProvider.of<AuthBloc>(context).state.userModel;
        return user.docId != currentUser?.docId;
      }).toList();
    }
    return [];
  }

  /// Filter messages by selected teacher
  List<ChatModel> _getFilteredMessages() {
    if (_selectedTeacherId == null) {
      return _chats;
    }
    return _chats.where((chat) => chat.senderId == _selectedTeacherId).toList();
  }

  /// Get selected teacher name for display
  String _getSelectedTeacherName() {
    if (_selectedTeacherId == null) return "";

    final teacher = _getGroupTeachers().firstWhere(
      (teacher) => teacher.docId == _selectedTeacherId,
      orElse: () => UserModel(),
    );

    return teacher.name ?? "Unknown Teacher";
  }

  /// Show teacher filter dialog
  _showTeacherFilterDialog() async {
    final teachers = _getGroupTeachers();
    if (teachers.isEmpty) {
      showToast("No teachers found in this group");
      return;
    }

    final result = await showDialog<String>(
      context: context,
      builder: (context) => TeacherFilterDialog(
        teachers: teachers,
        selectedTeacherId: _selectedTeacherId,
      ),
    );

    if (result != null) {
      setState(() {
        _selectedTeacherId = result == 'all' ? null : result;
      });
    }
  }

  _checkConversationExist() async {
    final String? id =
        await FirestoreService.checkConversationExist(users: _users);
    if (id != null) {
      _conversationId = id;
      _initChat(conversationId: _conversationId!);
    } else {
      _conversationId = null;
      _chatStream = null;
      _chats = [];
      // _roomName = _users
      //     .map((e) => e.name ?? "")
      // .reduce((value, element) => "$value, $element");
      setState(() {});
    }
  }

  _scrollToBottom() async {
    // await Future.delayed(Duration(milliseconds: 500));
    // _scrollController.animateTo(
    //   _scrollController.position.maxScrollExtent,
    //   duration: Duration(seconds: 1),
    //   curve: Curves.fastOutSlowIn,
    // );
    try {
      itemScrollController.jumpTo(
        index: 0,
        // duration: Duration.zero,
        // curve: Curves.ease,
      );
    } catch (_) {}
  }

  _updateHomeworkNotesLists() {
    _homeworkMessages =
        _chats.where((chat) => chat.isHomework == true).toList();
    _notesMessages = _chats.where((chat) => chat.isNotes == true).toList();

    // Update navigation visibility
    bool hasHomeworkOrNotes =
        _homeworkMessages.isNotEmpty || _notesMessages.isNotEmpty;
    if (_showHomeworkNotesNavigation != hasHomeworkOrNotes) {
      setState(() {
        _showHomeworkNotesNavigation = hasHomeworkOrNotes;
        if (!hasHomeworkOrNotes) {
          _currentNavigationIndex = 0;
        }
      });
    }

    // Reset index if current type has no messages
    List<ChatModel> currentList =
        _currentNavigationType == NavigationType.homework
            ? _homeworkMessages
            : _notesMessages;
    if (currentList.isEmpty && _homeworkMessages.isNotEmpty) {
      setState(() {
        _currentNavigationType = NavigationType.homework;
        _currentNavigationIndex = 0;
      });
    } else if (currentList.isEmpty && _notesMessages.isNotEmpty) {
      setState(() {
        _currentNavigationType = NavigationType.notes;
        _currentNavigationIndex = 0;
      });
    } else if (_currentNavigationIndex >= currentList.length &&
        currentList.isNotEmpty) {
      setState(() {
        _currentNavigationIndex = currentList.length - 1;
      });
    }
  }

  _jumpToMessage(ChatModel targetMessage) {
    int messageIndex = _chats.indexWhere((chat) => chat.id == targetMessage.id);
    if (messageIndex != -1) {
      try {
        itemScrollController.scrollTo(
          index: messageIndex,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      } catch (e) {
        // Fallback to jumpTo if scrollTo fails
        try {
          itemScrollController.jumpTo(index: messageIndex);
        } catch (_) {}
      }
    }
  }

  _onNavigationPrevious() {
    List<ChatModel> currentList =
        _currentNavigationType == NavigationType.homework
            ? _homeworkMessages
            : _notesMessages;

    if (_currentNavigationIndex > 0) {
      setState(() {
        _currentNavigationIndex--;
      });
      _jumpToMessage(currentList[_currentNavigationIndex]);
    }
  }

  _onNavigationNext() {
    List<ChatModel> currentList =
        _currentNavigationType == NavigationType.homework
            ? _homeworkMessages
            : _notesMessages;

    if (_currentNavigationIndex < currentList.length - 1) {
      setState(() {
        _currentNavigationIndex++;
      });
      _jumpToMessage(currentList[_currentNavigationIndex]);
    }
  }

  _onToggleNavigationType() {
    NavigationType newType = _currentNavigationType == NavigationType.homework
        ? NavigationType.notes
        : NavigationType.homework;

    List<ChatModel> newList =
        newType == NavigationType.homework ? _homeworkMessages : _notesMessages;

    if (newList.isNotEmpty) {
      setState(() {
        _currentNavigationType = newType;
        _currentNavigationIndex = 0;
      });
      _jumpToMessage(newList[0]);
    }
  }

  _onCloseNavigation() {
    setState(() {
      _showHomeworkNotesNavigation = false;
    });
  }

  _handleJumpToMessage() {
    if (widget.jumpToMessageId == null) return;

    // Find the message index
    int messageIndex =
        _chats.indexWhere((chat) => chat.id == widget.jumpToMessageId);
    if (messageIndex == -1) return;

    // Auto-show navigation if requested
    if (widget.showHomeworkNotesNavigation) {
      setState(() {
        _showHomeworkNotesNavigation = true;

        // Set navigation type based on message type or parameter
        if (widget.initialNavigationType == 'homework') {
          _currentNavigationType = NavigationType.homework;
        } else if (widget.initialNavigationType == 'notes') {
          _currentNavigationType = NavigationType.notes;
        } else {
          // Auto-detect based on message
          if (_chats[messageIndex].isHomework == true) {
            _currentNavigationType = NavigationType.homework;
          } else if (_chats[messageIndex].isNotes == true) {
            _currentNavigationType = NavigationType.notes;
          }
        }

        // Find the index in the filtered list
        List<ChatModel> currentList =
            _currentNavigationType == NavigationType.homework
                ? _homeworkMessages
                : _notesMessages;

        int navIndex =
            currentList.indexWhere((chat) => chat.id == widget.jumpToMessageId);
        if (navIndex != -1) {
          _currentNavigationIndex = navIndex;
        }
      });
    }

    // Jump to the message
    Future.delayed(const Duration(milliseconds: 500), () {
      try {
        itemScrollController.scrollTo(
          index: messageIndex,
          duration: const Duration(milliseconds: 800),
          curve: Curves.easeInOut,
        );
      } catch (e) {
        try {
          itemScrollController.jumpTo(index: messageIndex);
        } catch (_) {}
      }
    });
  }

  _showHomeworkNavigation() {
    if (_homeworkMessages.isNotEmpty) {
      setState(() {
        _showHomeworkNotesNavigation = true;
        _currentNavigationType = NavigationType.homework;
        _currentNavigationIndex = 0;
      });
      _jumpToMessage(_homeworkMessages[0]);
    }
  }

  _showNotesNavigation() {
    if (_notesMessages.isNotEmpty) {
      setState(() {
        _showHomeworkNotesNavigation = true;
        _currentNavigationType = NavigationType.notes;
        _currentNavigationIndex = 0;
      });
      _jumpToMessage(_notesMessages[0]);
    }
  }

  _onDownload(ChatModel chat, int index) async {
    try {
      setState(() {
        _isLoading = true;
      });

      if (kIsWeb) {
        // On web, trigger browser download
        setState(() {
          _isLoading = false;
        });

        String fileName = chat.message!.split('/').last;
        if (fileName.isEmpty) {
          fileName = 'downloaded_file';
        }

        await PlatformHelper.downloadFromUrl(
            url: chat.message!, fileName: fileName);
        showToast("Download started...");
        return;
      }

      final directory =
          await PlatformHelper.getApplicationDocumentsDirectoryCompat();
      if (directory == null) {
        setState(() {
          _isLoading = false;
        });
        showToast("File operations not supported on this platform");
        return;
      }

      // if (await file.exists()) {
      //   file.delete();
      // }
      String url = chat.message!;

      // Use Uri.parse() to parse the URL
      Uri uri = Uri.parse(url);

      // Extract the 'path' component from the URI
      String path = uri.path;

      path = path.split('/').last;

      path = Uri.decodeComponent(path);

      File file = File('${directory.path}/$path');
      file.create(recursive: true);
      Reference storageReference = FirestoreService.storageRef.child(path);
      await storageReference.writeToFile(file);
      _chats[index] = chat.copyWith(localPath: file.path);
      setState(() {
        _isLoading = false;
      });

      await PlatformHelper.openFile(file.path);
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showToast(e.toString());
    }
  }

  _onAddLink(String link) {
    setState(() {
      _tempLink = link;
      _tempFiles.clear();
      _tempPlatformFiles.clear();
    });
    if (link.startsWith("upi") ||
        link.contains("feepayindia.in/opay/index.php")) {
      _inputController.text = "key_dear_parent_payment_link_content".tr();
    }
  }

  _onLongMessagePress(int index) async {
    var isTeacher = BlocProvider.of<AuthBloc>(context).state.userModel?.type ==
        AppConstants.teacherType;
    var chat = _chats[index];
    bool canDelete = chat.senderId ==
        BlocProvider.of<AuthBloc>(context).state.userModel?.docId;
    final result = await showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        builder: (_) {
          return MessageOptionsModal(
            fromMe: chat.senderId == getProfile(context).docId,
            canDelete: canDelete,
            isTeacher: isTeacher,
            isGroupChat: (_conversationModel?.userIds?.length ?? 0) > 1,
          );
        });

    if (result == 'delete') {
      await FirestoreService.deleteMessage(
          chatModel: _chats[index],
          isLasted: index == 0,
          conversationId: _conversationId!);
    } else if (result == 'forward') {
      if (mounted) {
        showModalBottomSheet(
            context: context,
            isScrollControlled: true,
            backgroundColor: Colors.transparent,
            builder: (_) {
              return ForwardMessageModal(
                chatModel: _chats[index],
              );
            });
      }
    } else if (result == 'share') {
      var message = _chats[index];
      if (['image', 'video'].contains(message.type)) {
        if (message.localPath != null) {
          Share.shareXFiles([XFile(message.localPath!)]);
        } else {
          setState(() {
            _isLoading = true;
          });
          Helper.downloadMedia(
              link: message.message ?? "",
              onCompleted: (path) {
                setState(() {
                  _isLoading = false;
                });
                Share.shareXFiles([XFile(path)]);
                message = message.copyWith(localPath: path);
                _chats[index] = message;
              },
              onError: (error) {
                setState(() {
                  _isLoading = false;
                });
              });
        }
      } else {
        Share.share('${message.message}');
      }
    } else if (result == 'delivered_status') {
      push(
          context,
          DeliveredStatus(
              chatModel: _chats[index],
              conversationModel: _conversationModel!));
    } else if (result == 'reply') {
      setState(() {
        _replyModel = chat;
      });
    } else if (result == 'set_as_homework') {
      try {
        setState(() {
          _isLoading = true;
        });

        bool currentHomeworkStatus = _chats[index].isHomework ?? false;
        bool newHomeworkStatus = !currentHomeworkStatus;

        await FirestoreService.setMessageAsHomework(
          chatModel: _chats[index],
          conversationId: _conversationId!,
          isHomework: newHomeworkStatus,
        );

        // Update local state
        _chats[index] = _chats[index].copyWith(
          isHomework: newHomeworkStatus,
          homeworkSetAt: newHomeworkStatus
              ? DateTime.now().toUtc().toIso8601String()
              : null,
        );

        setState(() {
          _isLoading = false;
        });

        _updateHomeworkNotesLists();

        showToast(newHomeworkStatus
            ? "Message set as homework"
            : "Homework status removed");
      } catch (e) {
        setState(() {
          _isLoading = false;
        });
        showToast(e.toString());
      }
    } else if (result == 'set_as_notes') {
      try {
        setState(() {
          _isLoading = true;
        });

        bool currentNotesStatus = _chats[index].isNotes ?? false;
        bool newNotesStatus = !currentNotesStatus;

        await FirestoreService.setMessageAsNotes(
          chatModel: _chats[index],
          conversationId: _conversationId!,
          isNotes: newNotesStatus,
        );

        // Update local state
        _chats[index] = _chats[index].copyWith(
          isNotes: newNotesStatus,
          notesSetAt:
              newNotesStatus ? DateTime.now().toUtc().toIso8601String() : null,
        );

        setState(() {
          _isLoading = false;
        });

        _updateHomeworkNotesLists();

        showToast(
            newNotesStatus ? "Message set as notes" : "Notes status removed");
      } catch (e) {
        setState(() {
          _isLoading = false;
        });
        showToast(e.toString());
      }
    }
  }

  _leaveGroup() async {
    try {
      setState(() {
        _isLoading = true;
      });
      var user = BlocProvider.of<AuthBloc>(context).state.userModel!;
      await FirestoreService.leaveGroup(
          conversationId: _conversationId!, userId: user.docId!);
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        pop(context);
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showToast(e.toString());
    }
  }

  _onMenuTap(int index) async {
    switch (index) {
      case 0:
        setState(() {
          _showSearch = true;
        });
        break;
      case 1:
        var result = await push(
            context,
            ChangeRoomName(
              name: _roomName,
              image: _conversationModel?.image,
            ));
        if (result != null) {
          setState(() {
            _roomName = result['name'];
          });
          await FirestoreService.changeRoomName(
              roomId: _conversationId!,
              name: _roomName,
              image: result['image']);
        }
        break;
      case 2:
        var user = BlocProvider.of<AuthBloc>(context).state.userModel;
        var muted = _conversationModel?.mutedByUsers?.contains(user!.docId);
        await FirestoreService.muteNotificationChat(
            model: _conversationModel!, isMute: !muted!);
        break;
      case 3:
        push(context, ViewMembers(conversationId: _conversationId!));
        break;
      case 4:
        push(context, ViewMediasAndLinks(chats: _chats));
        break;
      case 5:
        await Helper.showActionDialog(
            context: context,
            title: "key_notifications".tr(),
            message: "key_are_you_want_to_leave_this_group".tr(),
            onClose: () {},
            onConfirm: () {
              _leaveGroup();
            });
      case 6:
        await Helper.showActionDialog(
            context: context,
            title: "key_notifications".tr(),
            message: "do_you_want_to_delete_this_group".tr(),
            onClose: () {},
            onConfirm: () {
              _deleteGroup();
            });
        break;
    }
  }

  int _totalSearch = 0;
  int _currentIndexSearch = 0;
  List<ChatModel> _searchedMessages = [];
  _searchMessage(String value) {
    if (value.isNotEmpty) {
      _searchedMessages = _chats
          .where((element) =>
              ['image', 'video'].contains(element.type) == false &&
              element.fileSize == null &&
              (element.message ?? "")
                  .toLowerCase()
                  .contains(value.toLowerCase()))
          .toList();
      setState(() {
        _totalSearch = _searchedMessages.length;
        if (_searchedMessages.isNotEmpty) {
          _currentIndexSearch = 0;
        } else {
          _currentIndexSearch = 0;
        }
      });
      if (_searchedMessages.isNotEmpty) {
        // var index = _chats.indexWhere((element) =>
        //     element.id == _searchedMessages[_currentIndexSearch].id);
        // _currentIndexSearch = index;
        _scrollToIndex();
      }
    } else {
      setState(() {
        _totalSearch = 0;
        _currentIndexSearch = 0;
      });
    }
  }

  _scrollToIndex({int? chatIndex}) {
    var index = 0;
    if (chatIndex != null) {
      index = chatIndex;
    } else {
      index = _chats.indexWhere(
          (element) => element.id == _searchedMessages[_currentIndexSearch].id);
    }

    try {
      itemScrollController.jumpTo(index: index);
    } catch (_) {}
  }

  @override
  void dispose() {
    FirestoreService.currentConversationId = null;
    super.dispose();
  }

  _onReplyTap(ChatModel chatModel) {
    var index = _chats.indexWhere((element) => element.id == chatModel.id);
    if (index != -1) {
      _scrollToIndex(
        chatIndex: index,
      );
    }
  }

  /// Convert FilePickerResult to List<File> with web support
  Future<List<File>> _convertFilePickerResultToFiles(
      FilePickerResult result) async {
    List<File> files = [];

    if (kIsWeb) {
      // On web, use bytes instead of paths
      for (var platformFile in result.files) {
        if (platformFile.bytes != null) {
          // Create a temporary file from bytes
          var tempFile = await Helper.saveBytesToTemporaryFile(
            uint8list: platformFile.bytes!,
            extension: platformFile.extension ?? 'tmp',
          );
          if (tempFile != null) {
            files.add(tempFile);
          }
        }
      }
    } else {
      // On mobile/desktop, use paths
      for (var path in result.paths) {
        if (path != null) {
          files.add(File(path));
        }
      }
    }

    return files;
  }

  /// Send media from bytes (for web compatibility)
  _onSendMediaBytes({
    required Uint8List bytes,
    required String fileName,
    required String extension,
  }) async {
    var senderId = BlocProvider.of<AuthBloc>(context).state.userModel!.docId!;
    var senderName = getProfile(context).name ?? "";
    var tempReply = _replyModel;
    var tempContent = _inputController.text;

    // Clear UI state immediately
    setState(() {
      _replyModel = null;
      _tempPlatformFiles.clear();
    });
    _inputController.clear();

    // Determine file type
    String type = 'file';
    if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp']
        .contains(extension.toLowerCase())) {
      type = 'image';
    } else if (['mp4', 'mov', 'avi', 'mkv', 'webm']
        .contains(extension.toLowerCase())) {
      type = 'video';
    }

    await _getConversationId(message: fileName);

    // Create optimistic media message
    final optimisticMessage = ChatModel.createOptimistic(
      message: tempContent.isNotEmpty ? tempContent : fileName,
      senderId: senderId,
      senderName: senderName,
      type: type,
      fileName: fileName,
      fileSize: bytes.length,
      fileContent: fileName, // For web, we'll use fileName as placeholder
      replyChat: tempReply,
    );

    // Add optimistic message to UI immediately
    setState(() {
      _chats.insert(0, optimisticMessage);
    });

    // Scroll to show new message
    _scrollToBottom();

    try {
      // For web, upload bytes directly to Firebase Storage
      String path = "${DateTime.now().millisecondsSinceEpoch}.$extension";
      Reference storageReference =
          FirestoreService.storageRef.child('medias').child(path);

      // Upload bytes directly
      UploadTask uploadTask = storageReference.putData(bytes);
      await uploadTask.whenComplete(() {});

      // Get download URL
      String downloadUrl = await storageReference.getDownloadURL();

      await FirestoreService.sendMediaMessage(
        replyChat: tempReply,
        conversationModel: _conversationModel!,
        fileThumbnail: null,
        path: downloadUrl,
        senderId: senderId,
        type: type,
        fileName: fileName,
        fileSize: bytes.length,
        content: tempContent,
      );
    } catch (e) {
      // Mark message as failed if upload/send fails
      if (mounted) {
        setState(() {
          final index = _chats
              .indexWhere((chat) => chat.tempId == optimisticMessage.tempId);
          if (index != -1) {
            _chats[index] = _chats[index].copyWith(
              isPending: false,
              isFailed: true,
            );
          }
        });
      }
      showToast("Failed to send media: ${e.toString()}");
    }
  }

  /// Send platform file (for web compatibility)
  _onSendPlatformFile(PlatformFile platformFile) async {
    try {
      if (kIsWeb && platformFile.bytes != null) {
        // On web, upload bytes directly to Firebase Storage
        await _onSendMediaBytes(
          bytes: platformFile.bytes!,
          fileName: platformFile.name.isNotEmpty
              ? platformFile.name
              : 'file.${platformFile.extension ?? 'tmp'}',
          extension: platformFile.extension ?? 'tmp',
        );
      } else if (!kIsWeb && platformFile.path != null) {
        // On mobile, use the file path
        var tempFile = File(platformFile.path!);
        await _onSendMedia(tempFile);
      } else {
        throw Exception(
            'Could not process platform file: missing bytes or path');
      }
    } catch (e) {
      showToast(e.toString());
    }
  }

  _deleteGroup() async {
    try {
      setState(() {
        _isLoading = true;
      });
      await FirestoreService.deleteGroup(groupId: _conversationId!);
      setState(() {
        _isLoading = false;
      });
      pop(context);
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showToast(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    bool isDark = AppColors.isDark;

    return BlocBuilder<AuthBloc, AuthState>(builder: (context, state) {
      // String teacherName = '';
      // if (state.userModel?.type == AppConstants.parentType ||
      //     (state.userModel?.type == AppConstants.teacherType &&
      //         (_conversationModel?.users!.length ?? 0) > 2)) {
      //   teacherName = _conversationModel?.teacherName ?? "";
      // }
      return GestureDetector(
        onTap: () {
          hideKeyboard(context);
        },
        child: OverlayLoading(
          isLoading: _isLoading,
          child: Scaffold(
            backgroundColor: AppColors.backgroundScaffold,
            appBar: AppBar(
              leading: IconButton(
                  onPressed: () {
                    pop(context);
                  },
                  icon: Icon(
                    Icons.arrow_back,
                    color: AppColors.white,
                  )),
              backgroundColor: AppColors.black,
              title: Builder(builder: (context) {
                if (widget.isNewMessage) {
                  return GestureDetector(
                    onTap: () async {
                      final result = await push(
                          context,
                          SearchParent(
                            users: _users,
                            showCall: false,
                          ));
                      if (result != null) {
                        var data = await push(
                            context,
                            ChangeRoomName(
                              name: "",
                              image: _imageGroup,
                            ));
                        if (data != null) {
                          setState(() {
                            _users = result;
                            _roomName = data['name'];
                            _imageGroup = data['image'];
                          });
                          await _checkConversationExist();
                        }
                      }
                    },
                    child: Builder(builder: (context) {
                      if (_users.isNotEmpty) {
                        return Text(
                          _roomName,
                          style: AppStyles.textSize12(color: AppColors.white),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        );
                      }
                      return Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 4),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(4),
                          color: Colors.white.withOpacity(0.2),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(right: 4),
                              child: Text(
                                "key_create_group".tr(),
                                style: AppStyles.textSize12(),
                              ),
                            ),
                            const Icon(
                              Icons.add_circle_outline_rounded,
                              color: AppColors.primary,
                              size: 20,
                            )
                          ],
                        ),
                      );
                    }),
                  );
                }
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      _roomName,
                      style: AppStyles.textSize12(color: AppColors.white),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (_selectedTeacherId != null)
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: AppColors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          "Filtered by: ${_getSelectedTeacherName()}",
                          style: AppStyles.textSize10(
                            color: AppColors.white,
                            fontWeight: FontWeight.w500,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                );
              }),
              centerTitle: true,
              elevation: 0,
              actions: [
                PopupMenuButton<int>(
                  // initialValue: 0,
                  icon: Icon(
                    Icons.more_horiz,
                    color: AppColors.white,
                  ),
                  color: AppColors.black,
                  // Callback that sets the selected popup menu item.
                  onSelected: _onMenuTap,
                  itemBuilder: (BuildContext context) => <PopupMenuEntry<int>>[
                    PopupMenuItem<int>(
                      value: 0,
                      child: Text(
                        'key_search'.tr(),
                        style: AppStyles.textSize14(),
                      ),
                    ),
                    if (state.userModel?.type == AppConstants.teacherType &&
                        ((_conversationModel?.users?.length ?? 0) > 2))
                      PopupMenuItem<int>(
                        value: 1,
                        child: Text(
                          'key_change_conversation_name'.tr(),
                          style: AppStyles.textSize14(),
                        ),
                      ),
                    PopupMenuItem<int>(
                      value: 2,
                      child: Builder(builder: (context) {
                        var muted = _conversationModel?.mutedByUsers
                            ?.contains(state.userModel!.docId);
                        if (muted == true) {
                          return Text(
                            'key_unmute_notification'.tr(),
                            style: AppStyles.textSize14(),
                          );
                        }
                        return Text(
                          'key_mute_notification'.tr(),
                          style: AppStyles.textSize14(),
                        );
                      }),
                    ),
                    if (state.userModel?.type == AppConstants.teacherType)
                      PopupMenuItem<int>(
                        value: 3,
                        child: Text(
                          'key_view_people_in_group'.tr(),
                          style: AppStyles.textSize14(),
                        ),
                      ),
                    PopupMenuItem<int>(
                      value: 4,
                      child: Text(
                        'key_view_media_file_and_link'.tr(),
                        style: AppStyles.textSize14(),
                      ),
                    ),
                    if (state.userModel?.type == AppConstants.teacherType)
                      PopupMenuItem<int>(
                        value: 5,
                        child: Text(
                          ((_conversationModel?.users?.length ?? 0) > 2)
                              ? 'key_leave_group'.tr()
                              : "key_exit".tr(),
                          style: AppStyles.textSize14(),
                        ),
                      ),
                    if (state.userModel?.docId == _conversationModel?.teacherId)
                      PopupMenuItem<int>(
                        value: 6,
                        child: Text(
                          'delete_group'.tr(),
                          style: AppStyles.textSize14(),
                        ),
                      ),
                  ],
                ),
              ],
            ),
            body: Column(
              children: [
                // const Divider(
                //   height: 1,
                //   color: AppColors.primary,
                // ),
                Expanded(
                  child: SafeArea(
                    child: Stack(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                // Colors.green[800]!,
                                // Colors.green[200]!,
                                isDark
                                    ? Colors.black.withOpacity(0.7)
                                    : Colors.grey.withOpacity(0.2),
                                Colors.white.withOpacity(0.3),
                              ],
                            ),
                          ),
                        ),
                        Column(
                          children: [
                            // Homework/Notes Navigation Bar
                            if (_showHomeworkNotesNavigation)
                              HomeworkNotesNavigation(
                                currentType: _currentNavigationType,
                                currentIndex: _currentNavigationIndex,
                                totalCount: _currentNavigationType ==
                                        NavigationType.homework
                                    ? _homeworkMessages.length
                                    : _notesMessages.length,
                                currentMessagePreview: () {
                                  List<ChatModel> currentList =
                                      _currentNavigationType ==
                                              NavigationType.homework
                                          ? _homeworkMessages
                                          : _notesMessages;
                                  if (currentList.isNotEmpty &&
                                      _currentNavigationIndex <
                                          currentList.length) {
                                    String message =
                                        currentList[_currentNavigationIndex]
                                                .message ??
                                            "";
                                    if (message.isEmpty) {
                                      String type =
                                          currentList[_currentNavigationIndex]
                                                  .type ??
                                              "";
                                      if (type == 'image') return "📷 Image";
                                      if (type == 'video') return "🎥 Video";
                                      if (type == 'file') return "📄 File";
                                      return "Message";
                                    }
                                    return message;
                                  }
                                  return "";
                                }(),
                                onPrevious: _onNavigationPrevious,
                                onNext: _onNavigationNext,
                                onClose: _onCloseNavigation,
                                onToggleType: _onToggleNavigationType,
                              ),
                            Expanded(
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 10,
                                  vertical: 10,
                                ),
                                child: ListConversations(
                                  onReplyTap: _onReplyTap,
                                  itemScrollController: itemScrollController,
                                  scrollOffsetController:
                                      scrollOffsetController,
                                  itemPositionsListener: itemPositionsListener,
                                  scrollOffsetListener: scrollOffsetListener,
                                  query: _query,
                                  onLongPress: _onLongMessagePress,
                                  onDownload: _onDownload,
                                  scrollController: _scrollController,
                                  teacher: _teacherInfo,
                                  chats: _getFilteredMessages(),
                                  onHomeworkTap: _showHomeworkNavigation,
                                  onNotesTap: _showNotesNavigation,
                                  onRetryMessage: _onRetryMessage,
                                ),
                              ),
                            ),
                            Builder(builder: (_) {
                              if (_showSearch) {
                                return SearchMessageInput(
                                    onClose: () {
                                      setState(() {
                                        _showSearch = false;
                                      });
                                    },
                                    currentIndex: _currentIndexSearch,
                                    total: _totalSearch,
                                    textEditingController:
                                        _searchMessageController,
                                    onNext: () {
                                      setState(() {
                                        _currentIndexSearch += 1;
                                      });

                                      _scrollToIndex();
                                    },
                                    onPrevious: () {
                                      setState(() {
                                        _currentIndexSearch -= 1;
                                      });
                                    },
                                    onChanged: (value) {
                                      setState(() {
                                        _query = value;
                                      });
                                      _searchMessage(value);
                                    });
                              }
                              if (widget.isNewMessage == false ||
                                  (widget.isNewMessage && _users.isNotEmpty)) {
                                return BlocBuilder<AuthBloc, AuthState>(
                                    builder: (context, state) {
                                  bool canChat = state.userModel?.type ==
                                      AppConstants.teacherType;
                                  if (canChat) {
                                    UserModel? student;
                                    try {
                                      if ((_conversationModel?.users?.length ??
                                              0) <=
                                          2) {
                                        student = _conversationModel?.users
                                            ?.firstWhere((element) =>
                                                element.docId !=
                                                state.userModel!.docId!);
                                      }
                                    } catch (e) {}
                                    return ChatInput(
                                      onVoiceRecording: () async {
                                        final result =
                                            await showModalBottomSheet(
                                                context: context,
                                                shape: RoundedRectangleBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(10),
                                                ),
                                                builder: (context) {
                                                  return VoiceRecording();
                                                });
                                        if (result != null) {
                                          _onSendMedia(File(result));
                                        }
                                      },
                                      onChanged: (p0) {
                                        setState(() {});
                                      },
                                      onClearReply: () {
                                        setState(() {
                                          _replyModel = null;
                                        });
                                      },
                                      replyModel: _replyModel,
                                      tempFiles: _tempFiles,
                                      tempPlatformFiles: _tempPlatformFiles,
                                      userModel: student,
                                      onClearLink: () {
                                        setState(() {
                                          _tempLink = null;
                                          _tempFiles.clear();
                                          _tempPlatformFiles.clear();
                                        });
                                      },
                                      tempLink: _tempLink,
                                      onAddLink: _onAddLink,
                                      onMuliFilePickerResult: (result) async {
                                        if (kIsWeb) {
                                          // On web, store platform files directly
                                          _tempPlatformFiles = result.files;
                                          _tempFiles.clear();
                                        } else {
                                          // On mobile, convert to File objects
                                          _tempFiles =
                                              await _convertFilePickerResultToFiles(
                                                  result);
                                          _tempPlatformFiles.clear();
                                        }
                                        setState(() {});
                                      },
                                      onFilePickerResult: (file) async {
                                        setState(() {
                                          _tempLink = null;
                                        });
                                        if (kIsWeb) {
                                          // On web, store platform files directly
                                          _tempPlatformFiles = file.files;
                                          _tempFiles.clear();
                                        } else {
                                          // On mobile, convert to File objects
                                          _tempFiles =
                                              await _convertFilePickerResultToFiles(
                                                  file);
                                          _tempPlatformFiles.clear();
                                        }
                                        setState(() {});
                                        // _onSendMedia(result);
                                      },
                                      onTap: () {
                                        _scrollToBottom();
                                      },
                                      focusNode: _focusNode,
                                      onSend: (htmlText) async {
                                        if (htmlText != null) {
                                          _onSendMessage(htmlText: htmlText);
                                        } else {
                                          if (_tempFiles.isNotEmpty) {
                                            var list = _tempFiles.toList();
                                            for (var file in list) {
                                              await _onSendMedia(file);
                                            }
                                          } else if (_tempPlatformFiles
                                              .isNotEmpty) {
                                            // Handle platform files (web)
                                            var list =
                                                _tempPlatformFiles.toList();
                                            for (var platformFile in list) {
                                              await _onSendPlatformFile(
                                                  platformFile);
                                            }
                                          } else {
                                            _onSendMessage();
                                          }
                                        }
                                      },
                                      textEditingController: _inputController,
                                    );
                                  }
                                  return Container();
                                });
                              }
                              return Container();
                            })
                          ],
                        ),
                        // Positioned(
                        //     bottom: 16, left: 20, right: 20, child: ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
            floatingActionButton: _showFilterFAB
                ? Container(
                    margin:
                        EdgeInsets.only(bottom: _replyModel != null ? 140 : 80),
                    child: Stack(
                      children: [
                        Opacity(
                          opacity: 0.8,
                          child: FloatingActionButton(
                            onPressed: _showTeacherFilterDialog,
                            backgroundColor: _selectedTeacherId != null
                                ? AppColors.primary
                                : AppColors.grey,
                            child: Icon(
                              _selectedTeacherId != null
                                  ? Icons.filter_alt
                                  : Icons.filter_list,
                              color: AppColors.white,
                            ),
                          ),
                        ),
                        if (_selectedTeacherId != null)
                          Positioned(
                            right: 0,
                            top: 0,
                            child: Container(
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: Colors.red,
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Icon(
                                Icons.check,
                                color: Colors.white,
                                size: 12,
                              ),
                            ),
                          ),
                      ],
                    ),
                  )
                : null,
          ),
        ),
      );
    });
  }
}
